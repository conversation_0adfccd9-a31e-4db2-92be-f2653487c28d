import queue


# 创建 队列存储获取的画面数据
q_video = queue.Queue()    # 画面显示
q_lcd = queue.Queue()
q_gps = queue.Queue()
q_mqtt_error = queue.Queue()
q_mqtt_get = queue.Queue()
q_mqtt_post = queue.Queue()  #   通用mqtt 转发队列
q_mqtt_post_ble =  queue.Queue()  # 蓝牙传感器消息专用队列
q_mqtt_setup = queue.Queue()
q_mqtt_gps = queue.Queue()
q_ble_data = queue.Queue()   # 蓝牙数据
q_beep_light = queue.Queue(1)   # 回响的肩灯


q_button_0 = queue.Queue()  # 按键消息队列
q_button_1 = queue.Queue()  # 按键消息队列
q_button_2 = queue.Queue()  # 按键消息队列
q_button_3 = queue.Queue()  # 按键消息队列


#q_msn_cammand = queue.Queue(1)   # 人员通知指令,只缓存1条.超过覆盖 按键响应
q_play_wav = queue.Queue()   # 音频播放指令队列
q_play_mqtt_get =  queue.Queue()  # MQTT回调音频
q_play_mqtt_post =  queue.Queue()  #mqtt 发送音频
q_laser = queue.Queue()  # 激光距离队列

q_power = queue.Queue()  # 电池电量数据

q_attitude = queue.Queue()  # 姿态队列
q_attitude_io = queue.Queue()  # 姿态队列


q_mqtt_modem_log = queue.Queue()  # 4G模块日志