import cv2
import time
import numpy as np  

from modules import spi_lcd

cap = cv2.VideoCapture(0)
cap.set(cv2.CAP_PROP_CONVERT_RGB, 0)  # 去除默认的RGB转换


width = cap.get(cv2.CAP_PROP_FRAME_WIDTH)
height = cap.get(cv2.CAP_PROP_FRAME_HEIGHT)

#帧率管理
frame_skip_count = 2  # 可调节的跳帧数，例如设为2表示每3帧显示一次
frame_count = 0  # 初始化帧计数器

while True:
    ret, frame = cap.read()
    if not ret:
        break
    frame_count += 1  # 增加帧计数器
    
    if frame_count % (frame_skip_count + 1) == 0:  # 每 (frame_skip_count + 1) 帧进行一次操作
    
        frame_img = frame[0:int(height/2)] #热成像画面 yuv422
        frame_img = cv2.cvtColor(frame_img, cv2.COLOR_YUV2BGR_YUYV)  # yuv转RGB
    
        # 温度图
        frame_temp_map = frame[int(height/2):]  # numpy   温度图 
    
        frame_temp_map = np.frombuffer(frame_temp_map, np.int16)   # 将数据合并成 int16 (每个单像素的2个uint8 合并成一个uint16)
        frame_temp_map = frame_temp_map/64 - 273.15   # 计算温度
        frame_temp_map = np.array(frame_temp_map).reshape(int(height/2),int(width))  # 恢复成二位数组
        frame_temp_map = np.asmatrix(frame_temp_map) # 转矩阵
        
        frame_temp_map = spi_lcd.zoom(frame_temp_map)   # 缩放温度图画面
    
 
        center_value = frame_temp_map[int(height/4), int(width/2)]   #  中心温度
        max_value = np.max(frame_temp_map)  
        min_value = np.min(frame_temp_map)
        # 获取最大值的索引
        max_index = np.argmax(frame_temp_map)      #最大值
        max_coords = np.unravel_index(max_index, frame_temp_map.shape)  #最大值坐标
        min_index = np.argmin(frame_temp_map)      #最小值
        min_coords = np.unravel_index(min_index, frame_temp_map.shape)  #最大值坐标

        
        frame = spi_lcd.zoom(frame_img)   # 缩放画面
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)  # 转换成屏幕需要的RGB
        
        # 渲染与绘制
        #spi_lcd.crosshair(frame) # 绘制中心点准心
        #spi_lcd.HUD_data(frame,max_value,center_value,min_value,4)
        
        # 绘制高温点, 红色
        #cv2.rectangle(frame, (max_coords[1]-3,max_coords[0]-3), (max_coords[1]+3,max_coords[0]+3), (255,0,0), -1)   # 绘制矩形 红色
        # 绘制高温点, 红色
        #cv2.rectangle(frame, (min_coords[1]-3,min_coords[0]-3), (min_coords[1]+3,min_coords[0]+3), (0,0,255), -1)   # 绘制矩形 蓝色
        
        spi_lcd.disp.ShowImage_cv(frame, 240, 240)  #输出至显示屏
        
cap.release()
 
    #cv2.imwrite('test.jpg',frame_img)
    #cap.close() 