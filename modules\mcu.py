import pyudev
import serial
import json

import logging 
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s - %(lineno)s - %(message)s')
Log = logging.getLogger(__name__)


# 遍历所有 tty 设备
'''
context = pyudev.Context()
for device in context.list_devices(subsystem='tty'):
    if 'ID_VENDOR' in device and 'ID_SERIAL_SHORT' in device:
        
        print(f"Device Node: {device.device_node}")
        print(f"  Vendor: {device['ID_VENDOR']}")
        print(f"  Product: {device.get('ID_MODEL', 'N/A')}")
        print(f"  Serial: {device['ID_SERIAL_SHORT']}")
        print(f"  UUID: {device.get('ID_USB_INTERFACES', 'N/A')}\n")
'''

# 获取 STM32芯片的设备路径
def  get_mcu_tty():
    context = pyudev.Context()
    for device in context.list_devices(subsystem='tty'):
        if 'ID_VENDOR' in device and 'ID_SERIAL_SHORT' in device:
            if device['ID_VENDOR'] == 'STMicroelectronics':
                return device.device_node
            
# 打开串口监听消息事件,推送进队列



if __name__ == '__main__':

    tty_url = get_mcu_tty()
    Log.info(tty_url)
    ser = serial.Serial(tty_url,115200,timeout=5)
     
    req = ''
    while True:
        req = ser.readline()
        print(req)
        if req != b'':
            # O = 20 距离事件, O:10 短按按键事件  O:11 长按按键事件
            #Log.debug(req)
            
            json_code = json.loads(req)
            
            if json_code['O'] == 20 :
                # 距离事件,消息推送至激光距离消息队列
                pass
            elif json_code['O'] == 10  :   # 短按事件
                IO = json_code['IO']
                print(f'短按IO:{IO}')
            elif json_code['O'] == 11  :   # 短按事件
                IO = json_code['IO']
                print(f'长按IO:{IO}')
            