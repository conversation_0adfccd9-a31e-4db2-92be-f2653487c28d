#!/bin/bash

echo "===== 开始一键安装配置 ====="

# 设置脚本可执行权限
chmod +x setup_basic.sh
chmod +x setup_config.sh
chmod +x setup_service.sh

# 依次执行脚本
./setup_basic.sh
./setup_config.sh

echo "是否配置自启动服务? (y/n)"
read setup_service_choice

if [ "$setup_service_choice" = "y" ] || [ "$setup_service_choice" = "Y" ]; then
    ./setup_service.sh
else
    echo "跳过服务配置"
fi

echo "===== 一键安装配置完成 ====="
echo "请检查上述输出是否有错误信息"
echo "请记得手动配置 SPI 和音频设置 (sudo raspi-config)" 