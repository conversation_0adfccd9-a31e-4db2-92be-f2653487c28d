import queue

# 队列大小配置 - 根据数据类型和重要性设置不同的限制
# 视频相关队列：限制较小，避免内存快速增长
VIDEO_QUEUE_SIZE = 10       # 视频帧队列，每帧约655KB，限制10帧约6.5MB
LCD_QUEUE_SIZE = 5          # LCD显示队列，处理速度快，限制5帧

# 通信相关队列：中等限制，保证通信稳定
MQTT_QUEUE_SIZE = 50        # MQTT消息队列，消息较小但重要
GPS_QUEUE_SIZE = 20         # GPS数据队列，数据更新频率中等
BLE_QUEUE_SIZE = 30         # 蓝牙数据队列，传感器数据

# 音频相关队列：较小限制，避免音频延迟和内存占用
AUDIO_QUEUE_SIZE = 10       # 音频队列，音频数据较大

# 按键和控制队列：小限制，响应及时
BUTTON_QUEUE_SIZE = 5       # 按键队列，事件型数据
CONTROL_QUEUE_SIZE = 10     # 控制类队列

# 传感器数据队列：中等限制
SENSOR_QUEUE_SIZE = 20      # 传感器数据队列

# 创建 队列存储获取的画面数据
q_video = queue.Queue(maxsize=VIDEO_QUEUE_SIZE)    # 画面显示 - 限制10帧防止内存溢出
q_lcd = queue.Queue(maxsize=LCD_QUEUE_SIZE)        # LCD显示 - 限制5帧，处理速度快

# GPS和定位相关队列
q_gps = queue.Queue(maxsize=GPS_QUEUE_SIZE)        # GPS数据 - 限制20条

# MQTT通信相关队列
q_mqtt_error = queue.Queue(maxsize=MQTT_QUEUE_SIZE)      # MQTT错误 - 限制50条
q_mqtt_get = queue.Queue(maxsize=MQTT_QUEUE_SIZE)        # MQTT接收 - 限制50条
q_mqtt_post = queue.Queue(maxsize=MQTT_QUEUE_SIZE)       # 通用mqtt转发队列 - 限制50条
q_mqtt_post_ble = queue.Queue(maxsize=MQTT_QUEUE_SIZE)   # 蓝牙传感器消息专用队列 - 限制50条
q_mqtt_setup = queue.Queue(maxsize=CONTROL_QUEUE_SIZE)   # MQTT设置 - 限制10条
q_mqtt_gps = queue.Queue(maxsize=GPS_QUEUE_SIZE)         # MQTT GPS - 限制20条
q_mqtt_modem_log = queue.Queue(maxsize=MQTT_QUEUE_SIZE)  # 4G模块日志 - 限制50条

# 蓝牙和传感器相关队列
q_ble_data = queue.Queue(maxsize=BLE_QUEUE_SIZE)         # 蓝牙数据 - 限制30条
q_beep_light = queue.Queue(maxsize=1)                    # 回响的肩灯 - 保持原有限制1条

# 按键消息队列
q_button_0 = queue.Queue(maxsize=BUTTON_QUEUE_SIZE)      # 按键消息队列 - 限制5条
q_button_1 = queue.Queue(maxsize=BUTTON_QUEUE_SIZE)      # 按键消息队列 - 限制5条
q_button_2 = queue.Queue(maxsize=BUTTON_QUEUE_SIZE)      # 按键消息队列 - 限制5条
q_button_3 = queue.Queue(maxsize=BUTTON_QUEUE_SIZE)      # 按键消息队列 - 限制5条

# 音频相关队列
#q_msn_cammand = queue.Queue(1)   # 人员通知指令,只缓存1条.超过覆盖 按键响应
q_play_wav = queue.Queue(maxsize=AUDIO_QUEUE_SIZE)       # 音频播放指令队列 - 限制10条
q_play_mqtt_get = queue.Queue(maxsize=AUDIO_QUEUE_SIZE)  # MQTT回调音频 - 限制10条
q_play_mqtt_post = queue.Queue(maxsize=AUDIO_QUEUE_SIZE) # mqtt发送音频 - 限制10条

# 传感器数据队列
q_laser = queue.Queue(maxsize=SENSOR_QUEUE_SIZE)         # 激光距离队列 - 限制20条
q_power = queue.Queue(maxsize=SENSOR_QUEUE_SIZE)         # 电池电量数据 - 限制20条
q_attitude = queue.Queue(maxsize=SENSOR_QUEUE_SIZE)      # 姿态队列 - 限制20条
q_attitude_io = queue.Queue(maxsize=CONTROL_QUEUE_SIZE)  # 姿态IO队列 - 限制10条