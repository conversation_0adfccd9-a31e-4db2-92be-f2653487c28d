# GPS数据接口

import serial,time,json
from datetime import datetime


import logging 
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s - %(lineno)s - %(message)s')
Log = logging.getLogger(__name__)


class GPS():
    def __init__(self,port,baudrate,timeout = 5):
        self.ser = serial.Serial(port,baudrate,timeout = timeout )

    def init(self):
        # 循环检查,等待串口AT响应正常
        while True:
            self.ser.write(b'AT\r\n')  # 请求AT响应
            try:
                req = self.ser.readline()  # 读取返回值
            except BaseException as error:
                Log.error(error)
                time.sleep(5)
                continue   # 跳过当前循环
            if req == b'AT\r\n':   # 确认设备通讯OK
                return True
            else:
                Log.error("init error:")
                time.sleep(5)
                continue   # 跳过当前循环
    # 开启GPS            
    def startGPS(self):   
        time.sleep(1)
        cmd = 'AT+CGNSPWR=1\r\n'   # 启动GPS
        cmd = bytes(cmd,encoding='utf-8')  # 转字符数组
        self.ser.write(cmd)  # 发送AT指令  
        time.sleep(1)
        req = self.ser.readline()  # 读取返回值
        Log.info(str(req,encoding='utf-8'))
        if req == b'OK\r\n':
            return True
        else:
            Log.error(str(req,encoding='utf-8'))
            return False
    # 启动 AGPS支持        
    def startAGPS(self):
        time.sleep(1)
        cmd = 'AT+CGNSAID=31,1,1,1\r\n'   # 启动AGPS
        cmd = bytes(cmd,encoding='utf-8')  # 转字符数组
        
        self.ser.write(cmd)  # 发送AT指令  
        time.sleep(1)
        req = self.ser.readline()  # 读取返回值
        Log.info(str(req,encoding='utf-8'))
        if req == cmd:
            return True
        else:
            Log.error(str(req,encoding='utf-8'))
            return False
    
    # 获取GPS数据      
    def get_daat(self):
        cmd = 'AT+CGNSINF\r'   # 获取当前GPS消息
        cmd = bytes(cmd,encoding='utf-8')  # 转字符数组
        
        self.ser.write(cmd)  # 发送AT指令
        time.sleep(1)
        req = self.ser.readlines()  # 读取返回值
        for i in req:
            if len(i) >= 60:
                str_gps_code = str(i,encoding='utf8')
                if str_gps_code[0] == '+' :
                    data = str_gps_code.split(': ')[1].split(',')
                    Log.debug(data)   # 打印GPS数据  debug
                    Time = data[2]
                    if Time != '':
                        dt = datetime.strptime(Time, "%Y%m%d%H%M%S")
                        formatted_time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
                    try:
                        Longitude = float(data[4])   # 纬度
                        Latitude = float(data[3])   # 经度
                        MSL_Altitude = float(data[5])  # 海拔高度   米
                        Speed = float(data[6])  # 速度  Km/hour
                        Course_Over_Ground = float(data[7])   # 地面航向
                    except BaseException as error:
                        Log.error(error)
                        json_gps = {"time":0,'gps':[0,0],'mls':0,'speed':0,'course':0} 
                    else:
                        json_gps = {"time":formatted_time_str,'gps':[Longitude,Latitude],'mls':MSL_Altitude,'speed':Speed,'course':Course_Over_Ground} 
                    return json.dumps(json_gps)
    # 获取GPS数据
    def get_gps_data(self):
        time.sleep(1)
        cmd = 'AT+CGNSINF\r'   # 获取当前GPS消息
        cmd = bytes(cmd,encoding='utf-8')  # 转字符数组
        
        self.ser.write(cmd)  # 发送AT指令
        time.sleep(1)
        req = self.ser.readlines()  # 读取返回值
        # 全局数据参数
        locationcode = 0
        Longitude = 0
        Latitude = 0
        formatted_time_str = 0
        MSL_Altitude = 0
        Speed = 0 
        Course_Over_Ground = 0
        for i in req:
            if len(i) >= 60:
                str_gps_code = str(i,encoding='utf8')
                if str_gps_code[0] == '+' :
                    data = str_gps_code.split(': ')[1].split(',')
                    Log.debug(data)   # 打印GPS数据  debug
                    locationcode = int(data[1])
                    if locationcode == 1:     # 如果定位状态为已定位
                        Time = data[2]
                        if Time != '':
                            dt = datetime.strptime(Time, "%Y%m%d%H%M%S")
                            formatted_time_str = dt.strftime("%Y-%m-%d %H:%M:%S")
                        try:
                            Longitude = float(data[4])   # 纬度
                            Latitude = float(data[3])   # 经度
                            MSL_Altitude = float(data[5])  # 海拔高度   米
                            Speed = float(data[6])  # 速度  Km/hour
                            Course_Over_Ground = float(data[7])   # 地面航向
                        except BaseException as error:
                            Log.error(error)
                        #json_gps = {"time":0,'gps':[0,0],'mls':0,'speed':0,'course':0} 

        return locationcode,Longitude,Latitude,formatted_time_str
    # 获取AGPS数据
    def get_agps_data(self):
        cmd = 'AT+CIPGSMLOC=1,1\r'
        cmd = bytes(cmd,encoding='utf-8')  # 转字符数组
        self.ser.write(cmd)  # 发送AT指令
        time.sleep(1)
        req = self.ser.readlines()  # 读取返回值
        Log.debug(req)
        #
        locationcode = 0
        Longitude = 0
        Latitude = 0 
        formatted_time_str = ''
        for i in req:
            if len(i) >40:                                # 寻找数据串中,足够长的数据
                str_agps_code = str(i,encoding='utf8')    # 转字符串
                if str_agps_code[0] =='+':              # 判断第一个字符是否是+号
                    data = str_agps_code.split(': ')[1].split(',')   # 以冒号分割后 再以逗号分割
                    locationcode = int( data[0])
                    if locationcode == 0:
                        Longitude = float(data[1])
                        Latitude = float(data[2])
                        formatted_time_str = data[3] + ' ' + data[4][:-2]
                    else:
                        Log.error(str_agps_code)    # AGPS locationcode 数据大于一则错误 见手册 86/263
                # locationcode 反馈需要与 GPS函数保持一致, 1 代表定位 0 代表未定位此处,进行转换
                if locationcode == 0:
                    locationcode = 1
                else:
                    locationcode = 0        
        return locationcode,Longitude,Latitude,formatted_time_str
    

if __name__ == '__main__':
    port = '/dev/ttyUSB0'
    baudrate = 115200
    
    import pyudev
    # 获取实际设备
    context = pyudev.Context()
    for device in context.list_devices(subsystem='tty'):
        if 'ID_VENDOR' in device and 'ID_SERIAL_SHORT' in device:
            if device['ID_VENDOR'] == '1a86':
                port =  device.device_node

    try:
        gps = GPS(port,baudrate)
        time.sleep(10)  # 等待GPS模块启动完成
        print(gps.ser.readall())  # 清空缓存
        print(gps.init())   # AT通讯检查
        print(gps.startGPS())  # GPS开机
        print(gps.startAGPS())  # AGPS开机
    except BaseException as error:
        print(error)
    
    else:
        while True:
            code,lon,lat,Time = gps.get_agps_data()
            print('agps: ',code,lon,lat,Time)
            code,lon,lat,Time = gps.get_gps_data()
            print('gps: ',code,lon,lat,Time)
            #cmd = gps.get_daat()    # 获取GPS数据
            time.sleep(3)