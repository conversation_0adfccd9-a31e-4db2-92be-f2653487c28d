import pyaudio
import paho.mqtt.client as mqtt
import ssl
    
    
    
audio = pyaudio.PyAudio()
audio_devices = audio.get_device_count()

# 获取所需的设备信息
#for i in range(audio_devices):
#    info = audio.get_device_info_by_index(i)
#    if  'USB Audio Device'  in info['name']:
#        print(info)
        
# 初始化mqtt 信息
uuid = '0000000000000000000000000000001'
broker = 'l91ae194.ala.cn-hangzhou.emqxsl.cn'
port = 8883
username = '0000000000000000000000000000001'
password = '0000000000000000000000000000001'
publish_topic = '/XF/auido/get/01'
subscribe_topic  = '/XF/auido/post/01'


# MQTT 消息处理回调函数
def on_connect(client, userdata, flags, reason_code, properties):
#def on_connect(client, userdata, flags, rc):
    print("连接成功，返回码: " + str(reason_code))
    # 订阅主题
    client.subscribe(publish_topic)

# 消息回调
def on_message(client, userdata, msg):
    audio_data = msg.payload # 获取音频消息字节码
    #play_stream.write(audio_data) # 写入播放流
    stream.write(audio_data) # 写入播放流

# 创建 MQTT 客户端
client_mqtt = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2)

# 设置用户名和密码
client_mqtt.username_pw_set(username, password)
# 启用 TLS/SSL
client_mqtt.tls_set(cert_reqs=ssl.CERT_REQUIRED)
# 连接到 MQTT 代理
client_mqtt.on_connect = on_connect
client_mqtt.on_message = on_message

# 建立服务器连接     
try:
    client_mqtt.connect(broker, port)
except Exception as e:
    print(f"连接失败: {e}")
    exit(1)


device_index = 1  # 替换为实际设备索引

        
stream = audio.open(format=pyaudio.paInt16, channels=1, rate=44100, input=True, output=True, frames_per_buffer=11025,input_device_index=device_index)   # 播放流 44100   

print("推流开始")

try:
    while True:
        if client_mqtt.is_connected():
            #data = record_stream.read(5120,exception_on_overflow = False)
            data = stream.read(11025,exception_on_overflow = False)
            client_mqtt.publish(publish_topic,data,qos=0)  # 推送音频至mqtt
        client_mqtt.loop()
except KeyboardInterrupt:
    print("中断推流")

# 清理资源
client_mqtt.disconnect()
stream.stop_stream()
stream.close()
#record_stream.stop_stream()
#record_stream.close()
#play_stream.stop_stream()
#play_stream.close()
audio.terminate()
