# 热成像推流断流问题分析与解决方案

## 问题现象

- **断流时间**：热成像画面cam1推流大概在20~30分钟后断流
- **本地正常**：本地LCD画面显示正常
- **无重试**：没有触发断流重试机制
- **队列堆积**：视频队列持续满载，频繁清理

## 根本原因分析

### 1. 🔴 队列堆积导致推流阻塞
**问题**：
- 队列持续满载（15帧），每2秒清理10帧
- 生产速度远超消费速度
- `q_video.get()` 可能阻塞，导致推流线程假死

**证据**：
```
2025-08-09 15:50:49,079 - 队列 q_video 清理了 7 个旧元素，当前大小: 5
2025-08-09 15:50:51,079 - 队列 q_video 清理了 10 个旧元素，当前大小: 5
```

### 2. 🔴 FFmpeg进程静默退出
**问题**：
- FFmpeg可能因为网络问题、数据异常等原因退出
- 代码中的 `pipe.poll() is None` 检查可能失效
- 没有看到FFmpeg退出的错误日志

### 3. 🔴 线程监控机制不足
**问题**：
- 线程监控只检查 `thread.is_alive()`
- 如果线程卡在阻塞操作中，`is_alive()` 仍返回True
- 无法检测到推流功能异常但线程未死的情况

### 4. 🟡 网络和编码参数不优化
**问题**：
- 码率可能过高，网络不稳定时容易断流
- 缺少网络自适应机制
- FFmpeg参数可能不适合长时间推流

## 解决方案

### 1. ✅ 强化断流检测机制

**新增功能**：
```python
# 全局状态监控
_img_net_status = {
    "last_frame_time": 0,
    "total_frames": 0,
    "restart_count": 0,
    "last_restart_time": 0
}

# 健康状态检查
def check_img_net_health():
    current_time = time.time()
    last_frame_time = _img_net_status.get("last_frame_time", 0)
    
    # 如果超过60秒没有处理帧，认为推流异常
    if current_time - last_frame_time > 60:
        return False, f"推流异常：{current_time - last_frame_time:.1f}秒无帧处理"
    
    return True, "推流正常"
```

### 2. ✅ 优化FFmpeg参数和错误处理

**改进**：
- 降低码率：从800k降低到500k
- 降低帧率：从15fps降低到10fps
- 增加错误检测：30秒无成功写入自动重启
- 改进错误处理：区分网络错误和数据错误

**新参数**：
```python
command = ['ffmpeg',
    '-loglevel', 'error',
    '-y', '-an',
    '-f', 'rawvideo',
    '-vcodec', 'rawvideo',
    '-pix_fmt', 'bgr24',
    '-s', target_size,
    '-framerate', '10',  # 降低输入帧率
    '-i', '-',
    '-c:v', 'h264',
    '-pix_fmt', 'yuv420p',
    '-preset', 'ultrafast',
    '-tune', 'zerolatency',
    '-r', '10',          # 降低输出帧率到10fps
    '-b:v', '500k',      # 降低码率，提高稳定性
    '-maxrate', '600k',  # 最大码率限制
    '-bufsize', '1200k', # 缓冲区大小
    '-g', '20',          # GOP大小
    '-f', 'flv',
    rtmp]
```

### 3. ✅ 增强队列管理

**改进**：
- 添加超时机制：`q_video.get(timeout=1)`
- 连续错误检测：连续10次错误自动重启
- 智能重启延迟：递增延迟，最大30秒

### 4. ✅ 改进线程监控

**新增**：
- 推流健康状态检查：每秒检查是否有帧处理
- 强制重启机制：健康检查失败时强制重启
- 资源清理：重启前清理队列和进程

### 5. ✅ 优化资源管理

**改进**：
```python
# 确保资源被释放
if pipe is not None:
    try:
        if pipe.stdin and not pipe.stdin.closed:
            pipe.stdin.close()
        if pipe.poll() is None:  # 进程仍在运行
            pipe.terminate()
            pipe.wait(timeout=5)
            if pipe.poll() is None:  # 强制杀死
                pipe.kill()
                pipe.wait(timeout=2)
    except Exception as e:
        Log.error(f"清理FFmpeg进程时出错: {e}")

# 清理队列中的旧数据
cleared_count = 0
while not q_video.empty() and cleared_count < 10:
    try:
        q_video.get_nowait()
        cleared_count += 1
    except:
        break
```

## 预期效果

### 断流检测
- **30秒检测**：30秒无成功写入自动重启
- **60秒检测**：60秒无帧处理强制重启
- **连续错误**：连续10次错误自动重启

### 稳定性提升
- **降低码率**：减少网络压力，提高稳定性
- **智能重启**：递增延迟，避免频繁重启
- **资源清理**：防止资源泄漏

### 监控改进
- **实时状态**：全局状态变量跟踪推流状态
- **详细日志**：区分不同类型的错误
- **健康检查**：主动检测推流功能异常

## 测试建议

### 功能测试
1. **长时间运行**：测试24小时连续推流
2. **网络中断**：模拟网络中断和恢复
3. **高负载**：测试高CPU/内存负载下的稳定性

### 监控验证
1. **断流检测**：验证各种断流检测机制是否生效
2. **自动重启**：确认重启机制正常工作
3. **日志分析**：检查错误日志的详细程度

## 关键改进点

### 🎯 核心改进
1. **多层检测**：30秒写入检测 + 60秒帧处理检测 + 连续错误检测
2. **智能重启**：根据错误类型调整重启延迟
3. **资源管理**：完善的进程和队列清理机制
4. **参数优化**：降低码率和帧率，提高稳定性

### 📊 监控指标
- 推流重启次数和间隔
- 队列堆积情况
- FFmpeg错误类型统计
- 网络连接稳定性

通过这些改进，应该能够有效解决20-30分钟断流问题，并确保断流后能够快速自动恢复。
