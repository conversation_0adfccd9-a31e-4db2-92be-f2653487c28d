# RTMP断流问题解决方案

## 🔍 当前问题分析

根据最新日志，问题已经明确：

### 核心问题：RTMP服务器端断开连接
```
av_interleaved_write_frame(): Broken pipe
Error writing trailer of rtmp://push.affppe.com/oakiot/YJ0QPjXfHU_cam1: Broken pipe
Error closing file rtmp://push.affppe.com/oakiot/YJ0QPjXfHU_cam1: Broken pipe
```

### 问题特征
- ✅ **断流检测正常**：系统能够检测到断流并自动重试
- ✅ **队列管理改善**：队列堆积问题已解决
- ✅ **FFmpeg启动正常**：能够成功启动推流
- ❌ **服务器断开连接**：2-3秒后服务器主动断开

## 🛠️ 已实施的改进

### 1. 强化网络诊断
```python
# 新增TCP连接测试
sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
sock.settimeout(5)
result = sock.connect_ex((server_host, server_port))
```

### 2. 优化FFmpeg参数
**降低资源消耗**：
- 帧率：15fps → 8fps
- 码率：500k → 300k
- 增加兼容性参数：`-profile:v baseline -level 3.0`
- 添加RTMP专用参数：`-rtmp_live live`

### 3. 智能重试策略
```python
# 连续失败处理
if consecutive_failures >= max_consecutive_failures:
    Log.error(f"连续{consecutive_failures}次推流失败，暂停60秒后重试")
    time.sleep(60)
    consecutive_failures = 0

# 根据错误类型调整等待时间
if "Broken pipe" in stderr_output:
    wait_time = min(10 + consecutive_failures * 2, 60)
    time.sleep(wait_time)
```

### 4. 连接状态监控
- 启动前TCP连接测试
- 成功推流30秒后重置失败计数器
- 详细的错误分类和日志

## 🔧 诊断工具

创建了 `rtmp_diagnostic.py` 诊断工具：

### 使用方法
```bash
python3 rtmp_diagnostic.py rtmp://push.affppe.com/oakiot/YJ0QPjXfHU_cam1
```

### 测试项目
1. **网络连接测试**：ping测试基本网络
2. **RTMP服务器测试**：TCP连接测试
3. **网络带宽测试**：下载速度测试
4. **FFmpeg推流测试**：实际推流测试

## 📊 可能的根本原因

### 1. 🔴 服务器端限制
- **连接数限制**：服务器可能限制同时连接数
- **推流时长限制**：可能有单次推流时长限制
- **码率限制**：服务器可能对码率有限制
- **认证问题**：推流密钥可能有时效性

### 2. 🟡 网络问题
- **4G网络不稳定**：信号强度27,99，可能不够稳定
- **运营商限制**：可能对RTMP流量有限制
- **NAT超时**：4G网络的NAT可能有较短的超时时间

### 3. 🟡 数据格式问题
- **编码参数不兼容**：服务器可能对H.264参数有特定要求
- **帧率过高**：即使降低到8fps可能仍然过高
- **分辨率问题**：640x480可能不是服务器期望的分辨率

## 🎯 建议的解决方案

### 立即行动
1. **运行诊断工具**：
   ```bash
   python3 rtmp_diagnostic.py rtmp://push.affppe.com/oakiot/YJ0QPjXfHU_cam1
   ```

2. **联系服务提供商**：
   - 确认推流密钥是否正确
   - 询问是否有连接时长限制
   - 确认推荐的编码参数

3. **测试不同参数**：
   - 尝试更低的码率（200k、100k）
   - 尝试更低的帧率（5fps、3fps）
   - 尝试不同的分辨率（320x240）

### 中期优化

1. **添加心跳机制**：
   ```python
   # 定期发送心跳包保持连接
   '-rtmp_live', 'live',
   '-rtmp_playpath', 'live',
   '-rtmp_buffer', '1000'
   ```

2. **实现流重连**：
   - 检测到断流时立即重连
   - 保持推流状态的连续性

3. **网络优化**：
   - 考虑使用有线网络测试
   - 优化4G网络配置

### 长期方案

1. **多服务器备份**：
   - 配置备用RTMP服务器
   - 主服务器失败时自动切换

2. **本地缓存**：
   - 实现本地视频缓存
   - 网络恢复时补发丢失的帧

## 🧪 测试计划

### 阶段1：诊断测试
```bash
# 运行完整诊断
python3 rtmp_diagnostic.py rtmp://push.affppe.com/oakiot/YJ0QPjXfHU_cam1

# 检查网络状况
ping -c 10 push.affppe.com

# 测试TCP连接
telnet push.affppe.com 1935
```

### 阶段2：参数优化测试
1. 测试极低码率（100k）
2. 测试极低帧率（3fps）
3. 测试小分辨率（320x240）

### 阶段3：网络环境测试
1. 有线网络测试
2. 不同时间段测试
3. 不同4G基站测试

## 📈 监控指标

### 关键指标
- **连接持续时间**：每次推流能维持多长时间
- **断流间隔**：断流重连的时间间隔
- **网络质量**：4G信号强度和稳定性
- **服务器响应**：RTMP服务器的响应时间

### 告警阈值
- 连续失败 > 5次：检查网络
- 连接时间 < 10秒：检查参数
- 重连间隔 > 60秒：检查服务器

## 🔍 下一步行动

1. **立即执行**：运行诊断工具获取详细信息
2. **联系服务商**：确认服务器配置和限制
3. **参数调优**：根据诊断结果调整FFmpeg参数
4. **网络测试**：在不同网络环境下测试

通过这些改进和诊断，应该能够找到并解决RTMP断流的根本原因。
