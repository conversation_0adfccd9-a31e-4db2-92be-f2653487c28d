#!/bin/bash

echo "===== 开始系统配置 ====="

# 创建USB权限规则
echo 'SUBSYSTEM=="usb", ATTR{idVendor}=="0bda", ATTR{idProduct}=="5830", MODE="0666"' | sudo tee /etc/udev/rules.d/99-camear.rules

# 配置DNS
echo '[Resolve]
DNS=1.1.1.1' | sudo tee /etc/systemd/resolved.conf
sudo systemctl enable systemd-resolved
sudo systemctl restart systemd-resolved

# 设置音量
amixer sset Speaker 37 || echo "Speaker设置失败，可能需要手动调整"
amixer sset Mic 35 || echo "Mic设置失败，可能需要手动调整"

echo "===== 系统配置完成 ====="
echo "注意：请手动执行以下操作："
echo "1. 使用 sudo raspi-config 开启SPI"
echo "2. 使用 sudo raspi-config 配置默认声卡"
echo "3. 如需禁用WiFi，请编辑 /boot/firmware/config.txt，添加 dtoverlay=disable-wifi"