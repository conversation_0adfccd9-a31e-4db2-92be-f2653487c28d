import threading
import time
import json


import q

import logging 
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s - %(lineno)s - %(message)s')
Log = logging.getLogger(__name__)

# 全局变量
q_msn_cammand = None

# 所需要的消息队列
q_video = q.q_video    # 画面显示
q_lcd = q.q_lcd
q_gps = q.q_gps
q_mqtt_error = q.q_mqtt_error
q_mqtt_post_ble = q.q_mqtt_post_ble
q_mqtt_post = q.q_mqtt_post
q_mqtt_get = q.q_mqtt_get
q_ble_data = q.q_ble_data



q_button_0 = q.q_button_0
q_button_1 = q.q_button_1
q_button_2 = q.q_button_2
q_button_3 = q.q_button_3

q_play_wav = q.q_play_wav



# MQTT 进程
def thre_mqtt_work(cfg):
    
    import paho.mqtt.client as mqtt
    import ssl
    
    # mqtt 链接参数相关配置
    uuid = cfg['MQTT']['username']
    broker = cfg['MQTT']['url']
    port = int(cfg['MQTT']['port'])
    username = cfg['MQTT']['username']
    password = cfg['MQTT']['password']
    subscribe_topic = cfg['MQTT']['topic_get']
    publish_topic = cfg['MQTT']['topic_post']
    error_topic =  cfg['MQTT']['topic_error']
    gpt_topic = cfg['MQTT']['topic_gps']
    # MQTT 消息处理回调函数
    def on_connect(client, userdata, flags, reason_code, properties):
        Log.info("连接成功，返回码: " + str(reason_code))
        # 订阅主题
        client.subscribe(subscribe_topic)
    
    
    # 消息回调
    def on_message(client, userdata, msg):
        global q_msn_cammand
        Log.info(f"接收到消息: {msg.topic} - {msg.payload.decode()}")
        # 解码消息
        try:
            data  = json.loads(msg.payload.decode())
        except BaseException as error:
            Log.error(error)
        else:  
            # 处理服务器下发的消息 判断如果是 type":"controlNotification" ,则是指令消息 
            if  data['type'] == 'controlNotification':
                q_msn_cammand = data # 解码后的消息直接存入
                # 需要给播放器队列,传递个播放消息
                q_play_wav.put(data['value']) 
                Log.debug(data)
    # 创建 MQTT 客户端
    client_mqtt = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2)
    # 设置用户名和密码
    client_mqtt.username_pw_set(username, password)
    # 启用 TLS/SSL
    client_mqtt.tls_set(cert_reqs=ssl.CERT_REQUIRED)
    # 连接到 MQTT 代理
    client_mqtt.on_connect = on_connect
    client_mqtt.on_message = on_message
    try:
        client_mqtt.connect(broker, port)
    except Exception as e:
        Log.info(f"连接失败: {e}")
        exit(1)

    # 循环监听
    #client_mqtt.loop_start()
    
    while True:
        client_mqtt.loop() 
        
        # 检查GPS队列.如果有数据,就推送
        if q_gps.empty()  != True:
            gps_data = q_gps.get()
            try:
                gps_data = json.loads(gps_data)
            except TypeError as e:
                Log.error(f"GPS数据格式错误: {e}")
            else:
                gps_data['uuid'] = uuid
                gps_data['type'] ='GPS'
                out_json = json.dumps(gps_data)
                client_mqtt.publish(gpt_topic,out_json,qos=0)
        # 检查蓝牙传感器数据,有就推送    
        if q_mqtt_post_ble.empty()  != True:
            mqtt_post_data = q_mqtt_post_ble.get()
            mqtt_post_data = json.loads(mqtt_post_data)
            mqtt_post_data['uuid'] = uuid
            mqtt_post_data['type'] ='sensor'
            out_json = json.dumps(mqtt_post_data)
            client_mqtt.publish(publish_topic,out_json,qos=0)
        
        # 错误消息队列检查,并发送到mqtt错误收集主题
        if q_mqtt_error.empty()  != True:
            mqtt_error_data = q_mqtt_error.get()
            try:
                mqtt_error_data = json.loads(mqtt_error_data)
            except TypeError as e:
                Log.error(f"错误消息格式错误: {e}")
                mqtt_error_data = {'error':str(mqtt_error_data)}
            mqtt_error_data['uuid'] = uuid
            mqtt_error_data['type'] ='error'
            out_json = json.dumps(mqtt_error_data)
            client_mqtt.publish(error_topic,out_json,qos=0)
        
        # 通用 上行消息队列
        if q_mqtt_post.empty() != True:
            mqtt_post_data = q_mqtt_post.get() # 获取数据
            out_json = json.dumps(mqtt_post_data)  # 编码json字符串
            client_mqtt.publish(publish_topic,out_json,qos=0)   # 直接推送数据
            Log.info(out_json)
        time.sleep(0.02)





# 串口与mqtt单模拟开发进程
def  serial_work(cfg):
    
    from modules import mcu
    import serial
    
    
    tty_url = mcu.get_mcu_tty()  # 获取mcu的串口url
    Log.info(tty_url)
    ser = serial.Serial(tty_url,115200,timeout=2)
    while True:
        req = ser.readline()
        if req != b'':
            # O = 20 距离事件, O:10 短按按键事件  O:11 长按按键事件
            Log.debug(req)      #日志
            json_code = json.loads(req)
            if json_code['O'] == 20 :
                q.q_laser.put(json_code['D'])# 距离事件,消息推送至激光距离消息队列
            elif json_code['O'] == 10  :   # 短按事件
                Log.info(json_code)     #日志
                IO = json_code['IO']
                if IO == 0:
                    q_button_0.put(0)
                elif IO == 1:
                    q_button_1.put(0)
                elif IO == 2:
                    q_button_2.put(0)
                elif IO == 3:
                    q_button_3.put(0)
            elif json_code['O'] == 11  :   # 长按事件
                Log.info(json_code)    #日志
                IO = json_code['IO']
                if IO == 0:
                    q_button_0.put(1)
                elif IO == 1:
                    q_button_1.put(1)
                elif IO == 2:
                    q_button_2.put(1)
                elif IO == 3:
                    q_button_3.put(1)
        time.sleep(0.02)
        
# 交互逻辑处理器

#首先 检查按键队列有任务,再检查mqtt来源任务不为空. 满足条件则发送应答消息(送到mqtt),然后把全局变量至None
def button_confirmation_work(cfg):
    
    global q_msn_cammand  # 使用全局变量
    while True:
        if q_button_0.empty()  != True:  # 如果按键0的队列不空     [暂定功能是确认功能键]
            button_type = q_button_0.get()  # 获取按键按下类型
            # 区分短按还是长按业务
            if button_type == 0:  # 短按
                # 判断 q_msn_cammand是否为None
                if q_msn_cammand != None:
                    # 正常响应业务 
                    # 像mqtt应答消息队列发送回送消息
                    q_mqtt_post.put(q_msn_cammand)
                    # 处理完成后清空消息缓存
                    q_msn_cammand = None
                
                Log.info('确认响应') 
            elif button_type == 1:      # SOS功能
                # 向mqtt发送一个sos消息   SOS消息内容待定
                json_data = {'type':'SOS'}
                # 像蓝牙发送一个专用请求消息(肩灯业务)
                
                
                Log.info(json_data)
                
        if q_button_1.empty()  != True:  # 如果按键1的队列不空     [暂定功能是伪色切换键]
            pass
        if q_button_2.empty()  != True:  # 如果按键2的队列不空
            pass
        if q_button_3.empty()  != True:  # 如果按键0的队列不空
            pass
        
        time.sleep(0.02)
    
if __name__ == '__main__':
    
    import yaml
    # 获取配置信息
    # 获取配置文件内容
    yaml_file_url = '/home/<USER>/main.yaml'
    ymal_file = open(yaml_file_url,'r',encoding='utf-8')
    cfg = yaml.load(ymal_file,Loader=yaml.FullLoader)
    ymal_file.close()
    
    
    
    
    # 线程控制
    t_serial_work = threading.Thread(target=serial_work,args = (cfg,))  
    t_button_confirmation_work = threading.Thread(target=button_confirmation_work,args = (cfg,))  
    t_thre_mqtt_work = threading.Thread(target=thre_mqtt_work,args = (cfg,))  # 创建一个线程    mqtt业务
    
    t_serial_work.start()  # 启动线程
    t_button_confirmation_work.start()  # 启动线程
    t_thre_mqtt_work.start()
    while True:
        time.sleep(1)