import asyncio
import struct
import time
from bleak import BleakClient, BleakScanner
from bleak.backends.device import BLEDevice
from bleak.backends.scanner import AdvertisementData
from bleak.backends.characteristic import BleakGATTCharacteristic

import logging 
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s - %(lineno)s - %(message)s')
Log = logging.getLogger(__name__)

import json
import q
q_mqtt_error = q.q_mqtt_error
q_ble_data = q.q_ble_data
q_beep_light = q.q_beep_light



import time

#cfg['cfg']['device_A']  #手环
#cfg['cfg']['device_B']  #压力表
#cfg['cfg']['device_C']  #气体检测仪


# modbus CRC校验计算
def crc16_modbus(data: bytes) -> int:
    crc = 0xFFFF
    for pos in range(len(data)):
        crc ^= data[pos]
        for _ in range(8):
            if (crc & 1) != 0:
                crc >>= 1
                crc ^= 0xA001
            else:
                crc >>= 1
    return crc

# 请求数据包拼包
def send_package(device_addr, code, data_addr, data_number):
    package_data = struct.pack('>B', device_addr) + struct.pack('>B', code) + struct.pack('>H', data_addr) + struct.pack('>H', data_number)
    crc = crc16_modbus(package_data)
    package_data = package_data + struct.pack('<H', crc)
    return package_data

# 蓝牙应答回调函数（设备1）
def notification_handler_1(characteristic: BleakGATTCharacteristic, data: bytearray):
    localtime = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())
    if len(data) == 33:     # 确保数据长度正确
        gas_concentration = struct.unpack('>I', data[3:7])[0]
        alarm = struct.unpack('>H', data[7:9])[0]
        gas_name = struct.unpack('6s', data[9:15])[0].decode("utf-8")
        magnification = struct.unpack('>H', data[15:17])[0]
        unit = struct.unpack('>H', data[17:19])[0]
        out_data = {'type':10,    # 此id会根据gas_name匹配值进行修改

                   'gas_concentration':gas_concentration,
                   'alarm':alarm,
                   'gas_name':gas_name,
                   'magnification':magnification,
                   'unit':unit
                   }
        out_json_data = json.dumps(out_data)
        q_ble_data.put(out_json_data) # 推送消息至队列
        #client_mqtt.publish(publish_topic,out_json_data,qos=0)  # 推送消息至
        Log.info(out_json_data)


# 广播式气瓶压力表(设备2)
def run_device_2callback(device: BLEDevice, advertisement_data: AdvertisementData,address_mac: str):
    if device.address == address_mac:
        #Log.info("%s: %r", device.address, advertisement_data.manufacturer_data)
        #  {305: b';\x04', 65505: b'\x00\x00\x00\x00\x00+\x16\x00\x00'}
        keys = list(advertisement_data.manufacturer_data.keys())
        if len(keys)  == 2:
            key1 = keys[0]
            key2 = keys[1]
            data = advertisement_data.manufacturer_data[key1]   # 判断消息收到的数据是否过少,如果太少则使用第二个key获取
            if len(data) <= 5:
                data = advertisement_data.manufacturer_data[key2]
            air_pressure = struct.unpack('>H', data[0:2])[0]   # 气压
            Time0 = struct.unpack('>B', data[2:3])[0]      # 剩余时间
            Time1 = struct.unpack('>H', data[3:5])[0]     # 使用时间
            
            power = struct.unpack('>B', data[5:6])[0]     # 电量
            temp = struct.unpack('>B', data[6:7])[0]     # 温度
            alarm0 = struct.unpack('>B', data[7:8])[0]   # 报警
            alarm1 = struct.unpack('>B', data[8:9])[0]   # 报警
            out_data = {'type':20,
                    'air_pressure':air_pressure,
                    'temp':temp,
                    'power':power,
                    'alarm':[alarm0,alarm1],
                    'Time':[Time0,Time1]          
            }
            out_json_data = json.dumps(out_data)
            q_ble_data.put(out_json_data) # 推送消息至队列
            Log.info(out_json_data)
            
            

# 手环应答回调(设备3)
def run_device_3callback(device: BLEDevice, advertisement_data: AdvertisementData,address_mac: str):
    if device.address == address_mac:
        #logger.info("%s: %r", device.address, advertisement_data.manufacturer_data)
        keys = list(advertisement_data.manufacturer_data.keys())
        key = keys[0]
        data = advertisement_data.manufacturer_data[key]
        power = struct.unpack('>B', data[4:5])[0]    # 电压 比例
        high_4_bits_power = power >> 4
        heart_rate = struct.unpack('>B', data[6:7])[0]  # 心率
        Temp = ((struct.unpack('>B', data[7:8])[0])+200) *0.1  # 体温
        BP_S = struct.unpack('>B', data[10:11])[0]  # 收缩压
        BP_D = struct.unpack('>B', data[11:12])[0]  # 舒张压
        #BO = struct.unpack('>B', data[12:13])[0]  # 血氧浓度
        
        out_data = {'type':30,
                'power':high_4_bits_power,
                'heart_rate':heart_rate,
                'BP':[BP_S,BP_D],
                'BO':0,
                'Temp':Temp
                }
        out_json_data = json.dumps(out_data)
        q_ble_data.put(out_json_data) # 推送消息至队列
        #client_mqtt.publish(publish_topic,out_json_data,qos=0)  # 推送消息至
        #print(publish_topic)
        Log.info(out_json_data)

# 手环 气压表二合一回调函数
def run_device_callback(device: BLEDevice, advertisement_data: AdvertisementData,address_mac1: str,address_mac2: str):
    # 气压表
    if device.address == address_mac1:
        #Log.info("%s: %r", device.address, advertisement_data.manufacturer_data)
        #  {305: b';\x04', 65505: b'\x00\x00\x00\x00\x00+\x16\x00\x00'}
        keys = list(advertisement_data.manufacturer_data.keys())
        if len(keys)  == 2:
            key1 = keys[0]
            key2 = keys[1]
            data = advertisement_data.manufacturer_data[key1]   # 判断消息收到的数据是否过少,如果太少则使用第二个key获取
            if len(data) <= 5:
                data = advertisement_data.manufacturer_data[key2]
            air_pressure = struct.unpack('>H', data[0:2])[0]   # 气压
            Time0 = struct.unpack('>B', data[2:3])[0]      # 剩余时间
            Time1 = struct.unpack('>H', data[3:5])[0]     # 使用时间
            
            power = struct.unpack('>B', data[5:6])[0]     # 电量
            temp = struct.unpack('>B', data[6:7])[0]     # 温度
            alarm0 = struct.unpack('>B', data[7:8])[0]   # 报警
            alarm1 = struct.unpack('>B', data[8:9])[0]   # 报警
            out_data = {'type':20,
                    'air_pressure':air_pressure,
                    'temp':temp,
                    'power':power,
                    'alarm':[alarm0,alarm1],
                    'Time':[Time0,Time1]          
            }
            out_json_data = json.dumps(out_data)
            q_ble_data.put(out_json_data) # 推送消息至队列
            Log.info(out_json_data)
    elif device.address == address_mac2:
        #logger.info("%s: %r", device.address, advertisement_data.manufacturer_data)
        keys = list(advertisement_data.manufacturer_data.keys())
        if len(keys) == 1:
            key = keys[0]
            data = advertisement_data.manufacturer_data[key]
            #Log.info(data.hex())
            
            power = struct.unpack('>B', data[4:5])[0]    # 电压 比例
            high_4_bits_power = power >> 4
            heart_rate = struct.unpack('>B', data[6:7])[0]  # 心率
            Temp = ((struct.unpack('>B', data[7:8])[0])+200) *0.1  # 体温
            BP_S = struct.unpack('>B', data[10:11])[0]  # 收缩压
            BP_D = struct.unpack('>B', data[11:12])[0]  # 舒张压
            BO = struct.unpack('>B', data[12:13])[0]  # 血氧浓度
        
            out_data = {'type':30,
                'power':high_4_bits_power,
                'heart_rate':heart_rate,
                'BP':[BP_S,BP_D],
                'BO':BO,
                'Temp':Temp
                }
            out_json_data = json.dumps(out_data)
            q_ble_data.put(out_json_data) # 推送消息至队列
            Log.info(out_json_data)         
        

#=========================================================================

# 设备1的主逻辑 (有毒气体传感器)
async def run_device_1(address_mac):
    characteristic_rx_1 = '00002af0-0000-1000-8000-00805f9b34fb'
    characteristic_tx_1 = '00002af1-0000-1000-8000-00805f9b34fb'
    # 最大重试次数
    try:   
        device = await BleakScanner.find_device_by_address(address_mac,timeout=10)
        if not device:
            raise Exception("Device not found during scanning.")
        async with BleakClient(device) as client:
            Log.info("Device 1 Connected")
            code_list = {0x0005, 0x0025, 0x0045, 0x0065}  #数据地址位
            await client.start_notify(characteristic_rx_1, notification_handler_1)
            for i in code_list:
                package_data = send_package(0x01, 0x03, i, 0x000e)
                await client.write_gatt_char(characteristic_tx_1, package_data)
                await asyncio.sleep(1.0)
            #await asyncio.sleep(5)
    except BaseException as error:
        out_data = {'error':'blue.py run_device_1 气体传感器 ' + str(error.__traceback__.tb_lineno) ,' error str': str(error) }
        out_json_data = json.dumps(out_data,ensure_ascii=False)
        #client_mqtt.publish(error_topic,out_json_data,qos=0)  # 错误消息推送
        q_mqtt_error.put(out_json_data) # 错误信息推送消息至队列
        error = str(error)
        Log.error(f"有毒气体传感器 error: { error }")
        
        

# 广播式气瓶压力表
async def run_device_2(address_mac):
    
    def callback(device: BLEDevice, advertisement_data: AdvertisementData):
        run_device_2callback(device, advertisement_data, address_mac)
    scanner = BleakScanner(callback)
    try:
        await scanner.start()
        await asyncio.sleep(2)
        await scanner.stop()
    except BaseException as error:
        out_data = {'error':'blue.py run_device_3 气瓶压力表 ' + str(error.__traceback__.tb_lineno) ,'error str': str(error) }
        out_json_data = json.dumps(out_data,ensure_ascii=False)
        q_mqtt_error.put(out_json_data) # 推送消息至队列
        error = str(error)
        Log.error(f"气瓶压力表 error: {error}") # 添加和出错的行号







# 手环
async def run_device_3(address_mac):
    
    def callback(device: BLEDevice, advertisement_data: AdvertisementData):
        run_device_3callback(device, advertisement_data, address_mac)
    scanner = BleakScanner(callback)
    try:
        await scanner.start()
        await asyncio.sleep(5)
        await scanner.stop()
    except BaseException as error:
        out_data = {'error':'blue.py run_device_3 手环 ' + str(error.__traceback__.tb_lineno) ,'error str': str(error) }
        out_json_data = json.dumps(out_data,ensure_ascii=False)
        q_mqtt_error.put(out_json_data) # 推送消息至队列
        error = str(error)
        Log.error(f"手环 error: {error}") # 添加和出错的行号
        
        
        
        
async def main(device1,device2,device3,device4):  # 环境气体传感器,气瓶压力表,手环,肩灯
    characteristic_rx_1 = '00002af0-0000-1000-8000-00805f9b34fb'
    characteristic_tx_1 = '00002af1-0000-1000-8000-00805f9b34fb'
    
    # 气瓶回调
    def callback(device: BLEDevice, advertisement_data: AdvertisementData):
        run_device_callback(device, advertisement_data, device2,device3)
    scanner = BleakScanner(callback)
    
    # 最大重试次数
    while True:
        try:   
            device = await BleakScanner.find_device_by_address(device1,timeout=3)
            if device:
                #raise Exception("Device not found during scanning.")
                async with BleakClient(device) as client:
                    Log.info("Device 1 Connected")
                    code_list = {0x0005, 0x0025, 0x0045, 0x0065}  #数据地址位
                    await client.start_notify(characteristic_rx_1, notification_handler_1)
                    for i in code_list:
                        package_data = send_package(0x01, 0x03, i, 0x000e)
                        await client.write_gatt_char(characteristic_tx_1, package_data)
                        await asyncio.sleep(0.1)
                    #await asyncio.sleep(5)

        except BaseException as error:
            out_data = {'error':'blue.py run_device_1 气体传感器 ' + str(error.__traceback__.tb_lineno) ,' error str': str(error) }
            out_json_data = json.dumps(out_data,ensure_ascii=False)
            #client_mqtt.publish(error_topic,out_json_data,qos=0)  # 错误消息推送
            q_mqtt_error.put(out_json_data) # 错误信息推送消息至队列
            error = str(error)
            Log.error(f"有毒气体传感器 error: { error }")
        # 气瓶+手环 扫描
        await scanner.start()
        await asyncio.sleep(3) # 扫描时间窗口
        await scanner.stop()
        
        if q_beep_light.empty() != True:
            Log.info("肩灯触发")
            q_beep_light.get()

            # 肩灯业务
            try:
                device_ble_light = await BleakScanner.find_device_by_address(device4,timeout=10)
                if  device_ble_light:
                    async with BleakClient(device_ble_light) as client_ble_light:
                        if not client_ble_light.is_connected:    # 连接设备
                            await client_ble_light.connect()

                #服务发现
                #services = client_ble_light.services
                #for service in services:
                #    print(f"Service UUID: {service.uuid}")
                #    for characteristic in service.characteristics:
                #        print(f" Characteristic UUID: {characteristic.uuid}")
    
                        if client_ble_light.is_connected:
                            Log.info("肩灯指令发送")
                            await client_ble_light.write_gatt_char('0000f001-0000-1000-8000-00805f9b34fb', bytearray([0x00]))
                            await asyncio.sleep(0.1)
                            await client_ble_light.write_gatt_char('0000f001-0000-1000-8000-00805f9b34fb', bytearray([0x01]))       
                            client_ble_light.disconnect()
            except BaseException as error:
                Log.info("jiandeng error")
        
if __name__ == "__main__":
    #time.sleep(15)
    while True:
        asyncio.run(run_device_1('66:22:AA:50:C3:FE'))
        asyncio.run(run_device_2('D9:69:A3:23:B9:86'))
        asyncio.run(run_device_3('CF:56:00:00:00:8E'))
