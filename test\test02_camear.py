import cv2
import time
import numpy as np
import asyncio

cap = cv2.VideoCapture(2)
cap.set(cv2.CAP_PROP_CONVERT_RGB, 0)  # 去除默认的RGB转换

width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
print(f"宽度: {width}, 高度: {height}")

async def process_frame(frame):
    frame_img = frame[0:int(height/2)]  # 热成像画面 yuv422
    frame_img = cv2.cvtColor(frame_img, cv2.COLOR_YUV2BGR_YUYV)  # yuv转RGB
    
    # 温度图
    frame_temp_map = frame[int(height/2):]  # numpy 温度图 
    frame_temp_map = np.frombuffer(frame_temp_map, np.int16)   # 将数据合并成 int16 (每个单像素的2个uint8 合并成一个uint16)
    frame_temp_map = frame_temp_map / 64 - 273.15   # 计算温度
    frame_temp_map = np.array(frame_temp_map).reshape(int(height/2), width)  # 恢复成二位数组
    frame_temp_map = np.mat(frame_temp_map)  # 转矩阵
    
    print(frame_temp_map[192, 144])  # 输出温度

async def main():
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        await process_frame(frame)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        cap.release()
        print("捕获结束")