#
import pyudev
import serial


import logging 
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s - %(lineno)s - %(message)s')
Log = logging.getLogger(__name__)


# 遍历所有 tty 设备
context = pyudev.Context()
for device in context.list_devices(subsystem='tty'):
    if 'ID_VENDOR' in device and 'ID_SERIAL_SHORT' in device:
        
        print(f"Device Node: {device.device_node}")
        print(f"  Vendor: {device['ID_VENDOR']}")
        print(f"  Product: {device.get('ID_MODEL', 'N/A')}")
        print(f"  Serial: {device['ID_SERIAL_SHORT']}")
        print(f"  UUID: {device.get('ID_USB_INTERFACES', 'N/A')}\n")



# USB_Single_Serial



def  get_mcu_tty(VENDOR):
    context = pyudev.Context()
    for device in context.list_devices(subsystem='tty'):
        if 'ID_VENDOR' in device and 'ID_SERIAL_SHORT' in device:
            if device['ID_VENDOR'] == VENDOR:
                return device.device_node
            
            
            
            
            
if __name__ == '__main__':
    print(get_mcu_tty('1a86'))