# 测试获取摄像头图像

# 通过检查相机名称的方式获取对应的voideo编号,确保获取的vodeo编码是对应相机
import os

def get_cam_img(cam_id):
    val = os.popen('v4l2-ctl --list-devices')  
    camList = val.readlines()
    for i in range(len(camList) - 1): # 减1是为了防止超出索引范围
        cam = camList[i]
        if cam_id in cam:   # 热成像
            return camList[i + 1][1:-1]
    return None

if __name__ == '__main__':
    cam_id = 'USB Camera'    # 热成像摄像头名称
    img_path = get_cam_img(cam_id)
    print('热成像',img_path)
    
    
    cam_id = 'USB HD Camera'    # 可见光摄像头名称
    img_path = get_cam_img(cam_id)
    print('可见光',img_path)
