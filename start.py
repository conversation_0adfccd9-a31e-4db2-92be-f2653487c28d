import os
# 获取当前脚本文件的绝对路径
current_file_path = os.path.abspath(__file__)
# 获取文件所在的目录
current_dir = os.path.dirname(current_file_path)
# 切换工作目录到文件所在的目录
os.chdir(current_dir)

import threading
import time
import json,base64
import subprocess
import serial

import cv2
import numpy as np

import logging 
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s - %(lineno)s - %(message)s')
Log = logging.getLogger(__name__)


from modules import get_cam_number as cam_num  
from modules  import camear_config
from modules import audio as AUDIO
audio = AUDIO.Audio()  # 实例化音频对象 


# 全局变量
q_msn_cammand = None
SOS = False  # SOS按键状态

# 4G模块数据全局变量
modem_imei = ""
modem_iccid = ""
modem_uicc = ""
modem_signal = ""
modem_servingcell = {}  # 存储基站小区信息

# 创建 队列存储获取的画面数据
import q
q_video = q.q_video    # 画面显示
q_lcd = q.q_lcd
q_gps = q.q_gps                        # GPS定位信息
q_mqtt_error = q.q_mqtt_error
q_mqtt_post_ble = q.q_mqtt_post_ble    # mqtt传感器
q_mqtt_post = q.q_mqtt_post            # mqtt通用消息
q_mqtt_get = q.q_mqtt_get
q_ble_data = q.q_ble_data
q_beep_light =q.q_beep_light  #肩灯控制响应队列
# 新增4G模块信息MQTT队列
q_mqtt_modem_log = q.q_mqtt_modem_log  # 4G模块日志队列

# 距离数据队列
q_laser = q.q_laser
laser  = 0

q_power =q.q_power
power  = 0
# 姿态队列
q_attitude = q.q_attitude
q_attitude_io = q.q_attitude_io

# 按键队列
q_button_0 = q.q_button_0
q_button_1 = q.q_button_1
q_button_2 = q.q_button_2
q_button_3 = q.q_button_3

# 音频队列
q_play_wav = q.q_play_wav
q_play_mqtt_get = q.q_play_mqtt_get      # 语音发送
q_play_mqtt_post = q.q_play_mqtt_post    # 语音接收


width = 0
height = 0    # 特别注意,高度是真实尺寸的一半,画面的下半部分是温度图


# 直播推送开关
live_start = True

# 队列监控函数
def queue_monitor(cfg):
    """
    监控所有队列的堆积情况，每10秒记录一次日志
    """
    while True:
        # 收集所有队列的大小信息
        queue_sizes = {
            "q_video": q_video.qsize(),
            "q_lcd": q_lcd.qsize(),
            "q_gps": q_gps.qsize(),
            "q_mqtt_error": q_mqtt_error.qsize(),
            "q_mqtt_post_ble": q_mqtt_post_ble.qsize(),
            "q_mqtt_post": q_mqtt_post.qsize(),
            "q_mqtt_get": q_mqtt_get.qsize(),
            "q_ble_data": q_ble_data.qsize(),
            "q_beep_light": q_beep_light.qsize(),
            "q_mqtt_modem_log": q_mqtt_modem_log.qsize(),
            "q_laser": q_laser.qsize(),
            "q_power": q_power.qsize(),
            "q_attitude": q_attitude.qsize(),
            "q_attitude_io": q_attitude_io.qsize(),
            "q_button_0": q_button_0.qsize(),
            "q_button_1": q_button_1.qsize(),
            "q_button_2": q_button_2.qsize(),
            "q_button_3": q_button_3.qsize(),
            "q_play_wav": q_play_wav.qsize(),
            "q_play_mqtt_get": q_play_mqtt_get.qsize(),
            "q_play_mqtt_post": q_play_mqtt_post.qsize()
        }
        
        # 过滤出大小不为0的队列
        non_empty_queues = {k: v for k, v in queue_sizes.items() if v > 0}
        
        if non_empty_queues:
            Log.info(f"队列堆积情况: {non_empty_queues}")
        else:
            Log.debug("所有队列均为空")
        
        # 检查是否有严重堆积的队列（大于10个元素）
        critical_queues = {k: v for k, v in queue_sizes.items() if v > 10}
        if critical_queues:
            Log.warning(f"队列严重堆积: {critical_queues}")
            
        time.sleep(10)  # 每10秒检查一次

# 直播推流可见光画面
def camear_live(cfg):
    global width,height
    import subprocess
    
    cam_id = 'USB HD Camera'    # 可见光摄像头名称 
    video_url = cam_num.get_cam_img(cam_id)
    if not video_url:
        Log.error("找不到可见光摄像头，推流服务无法启动")
        return  # 如果找不到摄像头，直接退出

    Log.info(f'camera_live: 已找到摄像头 {video_url}')
    rtmp = cfg['video']['url']+'cam0'
    
    retry_count = 0
    max_retries = 10  # 最大重试次数
    
    while True:  # 外层循环，确保推流服务持续运行
        try:
            # 每次循环前重新检查摄像头
            if retry_count % 3 == 0:  # 每3次重试检查一次摄像头
                new_video_url = cam_num.get_cam_img(cam_id)
                if not new_video_url:
                    Log.error("可见光摄像头已断开，停止推流服务")
                    return  # 如果摄像头不可用，直接退出函数
                elif new_video_url != video_url:
                    video_url = new_video_url
                    Log.info(f"切换到新的摄像头: {video_url}")
            
            command = ['ffmpeg',
                '-loglevel', 'warning',  # 显示警告和错误信息
                '-f', 'v4l2',
                '-input_format', 'mjpeg',
                '-video_size', '1280x720',
                '-framerate', '15',
                '-i', video_url,
                '-vf', 'format=yuvj420p',
                '-vcodec', 'h264',
                '-preset', 'ultrafast',
                '-tune', 'zerolatency',
                '-f', 'flv',
                rtmp]
            
            Log.info(f"启动可见光推流，命令参数: {' '.join(command)}")
            process = subprocess.Popen(command, shell=False, stderr=subprocess.PIPE)
            
            # 监控ffmpeg进程状态
            while process.poll() is None:
                time.sleep(1)  # 每秒检查一次进程状态
            
            # 进程退出后收集错误信息
            exit_code = process.returncode
            stderr_output = process.stderr.read().decode('utf-8', errors='ignore')
            
            if exit_code != 0:
                Log.error(f"可见光推流异常退出，退出码: {exit_code}")
                if stderr_output:
                    Log.error(f"错误信息: {stderr_output}")
                
                # 检查是否是找不到设备的错误
                if "No such file or directory" in stderr_output or "Cannot open video device" in stderr_output:
                    Log.error("摄像头设备无法访问，停止推流服务")
                    return  # 如果是设备问题，直接退出
            else:
                Log.info("可见光推流正常结束")
                
            retry_count += 1
            if retry_count > max_retries:
                Log.error(f"可见光推流失败达到最大重试次数({max_retries})，进入30秒冷却期")
                time.sleep(30)  # 冷却期
                retry_count = 0
                
        except Exception as e:
            Log.error(f"可见光推流发生异常: {str(e)}")
            retry_count += 1
        
        # 确保资源被释放
        if 'process' in locals() and process:
            try:
                process.terminate()
                process.wait(timeout=3)
            except:
                pass
            
        Log.info(f"可见光推流将在5秒后重试 (尝试 {retry_count}/{max_retries})...")
        time.sleep(5)  # 短暂延迟后重试

# 获取画面,分别存储到对应的消息队列
def camear_get(cfg):
    global width,height  # 全局变量,用于存储画面宽高
    
    cam_id = 'USB Camera'    # 热成像摄像头名称 最终是从配置文件读取
    
    video_url = cam_num.get_cam_img(cam_id)
    Log.info(f'camera_thermal: {video_url}')
    try:
        video_url_number = int(video_url[-1])   # 热成像摄像头序号
    except BaseException as e:
        Log.error(f"{e},camera thermal quit")   
        return ''
    
    cap = cv2.VideoCapture(video_url_number)
    cap.set(cv2.CAP_PROP_CONVERT_RGB, 0)  # 去除默认的RGB转换
    
    # 设置摄像头分辨率
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 384)  # 设置宽度
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 576)  # 设置高度
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)) # 获取画面宽高
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        frame = frame[0]
        if live_start == True:
            q_video.put(frame)    # 推流用
        q_lcd.put(frame)          # 屏显用


       # finally:
            # 确保摄像头资源被释放
       #     if 'cap' in locals() or 'cap' in globals():
       #         cap.release()

# 渲染画面至LCD
def lcd_work(cfg):
    
    global width,height
    global laser,power
    from modules import spi_lcd
    
    frame_skip_count = 1  # 可调节的跳帧数，例如设为2表示每3帧显示一次
    frame_count = 0  # 初始化帧计数器
    
    while True:
        if q_lcd.empty()  != True:
            frame = q_lcd.get()  # 获取帧
            frame_count += 1  # 增加帧计数器
            if frame_count % (frame_skip_count + 1) == 0:  # 每 (frame_skip_count + 1) 帧进行一次操作
                
                frame_img = frame[0:221184] # 热成像画面 yuv422  一维数组
                frame_img = frame_img.reshape((288,384, 2))  #将一维数组frame 总长度总长655360 转换为二维数组384x288*2 默认数据小端序
    
                frame_img = cv2.cvtColor(frame_img, cv2.COLOR_YUV2BGR_YUYV)  # yuv转RGB   
                
                # 温度数据
                # 温度图

                frame_temp_map = frame[221184:442368]  # numpy   温度图 
            
                frame_temp_map = frame_temp_map.view('int16')   # 将数据合并成 int16 
                frame_temp_map = frame_temp_map/64 - 273.15   # 计算温度
                frame_temp_map = np.array(frame_temp_map).reshape(288,384)  # 恢复成二位数组
                frame_temp_map = np.asmatrix(frame_temp_map) # 转矩阵
                
                frame_temp_map = spi_lcd.zoom(frame_temp_map)   # 缩放温度图画面
            
        
                center_value = frame_temp_map[int(288/2), int(384/2)]   #  中心温度
                
                max_value = np.max(frame_temp_map)  
                min_value = np.min(frame_temp_map)
                # 获取最大值的索引
                max_index = np.argmax(frame_temp_map)      #最大值
                max_coords = np.unravel_index(max_index, frame_temp_map.shape)  #最大值坐标
                min_index = np.argmin(frame_temp_map)      #最小值
                min_coords = np.unravel_index(min_index, frame_temp_map.shape)  #最大值坐标

                
                #print(frame_img.shape)
                frame_img = spi_lcd.zoom(frame_img)   # 缩放画面
                #print(frame_img.shape)
                # 转换颜色并显示图像
                frame_img = cv2.cvtColor(frame_img, cv2.COLOR_BGR2RGB)  # 转换成屏幕需要的RGB
                # 绘制屏幕元素
                spi_lcd.crosshair(frame_img,119,119) # 绘制中心点准心 ,缩放后的像素是240x240
                
                spi_lcd.crosshair(frame_img,max_coords[1],max_coords[0],(255,0,0)) # 绘制高温准星 ,红色
                spi_lcd.crosshair(frame_img,min_coords[1],min_coords[0],(0,0,255)) # 绘制低温准星 ,蓝色
                spi_lcd.HUD_data(frame_img,max_value,center_value,min_value,laser,power)  # 绘制计量数据

            
                # 支持画面旋转
                frame_img = cv2.rotate(frame_img,cv2.ROTATE_90_COUNTERCLOCKWISE)  # 顺时针90 ROTATE_90_CLOCKWISE   逆时针 ROTATE_90_COUNTERCLOCKWISE
                #frame = cv2.rotate(frame,cv2.ROTATE_180)
                
                spi_lcd.disp.ShowImage_cv(frame_img, 240, 240)
            else:
                time.sleep(0.01)
        else:
            time.sleep(0.01)


# 渲染热成像画面推送至阿里云
def img_net(cfg):
    global laser, width, height
    from modules import spi_lcd
    import subprocess
    
    # 等待摄像头初始化完成
    retry_count = 0
    while (width == 0 or height == 0) and retry_count < 30:
        time.sleep(1)
        retry_count += 1
        Log.info(f"等待摄像头初始化，尝试次数: {retry_count}")
    
    if width == 0 or height == 0:
        Log.error("摄像头初始化超时，使用默认分辨率384x288")
        width, height = 384, 288  # 使用默认分辨率
    
    # 热成像的实际分辨率是384x288
    size = (384, 288)  # 使用固定分辨率，避免计算错误
    sizeStr = "384x288"  # 直接使用字符串，避免格式化问题
    
    # 插值后的分辨率（保持宽高比）
    target_width = 640
    target_height = int(288 * (target_width / 384))  # 保持原始宽高比
    target_size = f"{target_width}x{target_height}"
    
    rtmp = cfg['video']['url']+'cam1'
    Log.info(f'img_net: {rtmp}, 原始分辨率: {sizeStr}, 插值后分辨率: {target_size}')
    
    while True:  # 外层循环，确保推流服务持续运行
        try:
            command = ['ffmpeg',
                #'-loglevel', 'warning',  # 只显示警告和错误
                '-y', '-an',
                '-f', 'rawvideo',
                '-vcodec', 'rawvideo',
                '-pix_fmt', 'bgr24',
                '-s', target_size,    # 使用插值后的分辨率
                '-framerate', '15',  # 指定输入帧率为15fps
                '-i', '-',
                '-c:v', 'h264',
                '-pix_fmt', 'yuv420p',
                '-preset', 'ultrafast',
                '-tune', 'zerolatency',
                '-r', '15',          # 指定输出帧率为15fps
                #'-b:v', '800k',      # 指定视频码率800kbps，提高图像质量
                #'-maxrate', '1000k', # 最大码率限制
                #'-bufsize', '2000k', # 缓冲区大小
                '-g', '30',          # GOP大小，2秒一个关键帧
                '-f', 'flv',
                rtmp]
            
            Log.info(f"启动ffmpeg推流，命令参数: {' '.join(command)}")
            pipe = subprocess.Popen(command, shell=False, stdin=subprocess.PIPE, stderr=subprocess.PIPE)
            
            frame_skip_count = 1
            frame_count = 0
            last_frame_time = time.time()
            
            while pipe.poll() is None:  # 内层循环处理视频帧，检查ffmpeg是否在运行
                if q_video.empty():
                    time.sleep(0.01)
                    continue
                    
                frame = q_video.get()
                frame_count += 1
                
                # 控制处理帧率
                current_time = time.time()
                if current_time - last_frame_time < 0.066:  # 约15fps的处理上限
                    continue
                
                if frame_count % (frame_skip_count + 1) == 0:  # 跳帧处理
                    try:
                        # 热成像处理
                        frame_img = frame[0:221184]  # 热成像画面 yuv422
                        frame_temp_map = frame[221184:]  # 温度图
                        
                        # 检查数据完整性
                        if len(frame_img) != 221184:
                            Log.warning(f"帧数据大小异常: {len(frame_img)}, 跳过此帧")
                            continue
                            
                        frame_img = frame_img.reshape((288, 384, 2))
                        frame_img = cv2.cvtColor(frame_img, cv2.COLOR_YUV2BGR_YUYV)  # yuv转BGR

                        # 温度数据处理
                        try:
                            frame_temp_map = np.frombuffer(frame_temp_map, np.int16)
                            frame_temp_map = frame_temp_map/64 - 273.15   # 计算温度
                            frame_temp_map = np.array(frame_temp_map).reshape(288, 384)  # 恢复成二维数组
                            frame_temp_map = np.asmatrix(frame_temp_map) # 转矩阵

                            center_value = frame_temp_map[int(288/2), int(384/2)]   #  获取中心温度
                            max_value = np.max(frame_temp_map)    # 获取最大温度
                            min_value = np.min(frame_temp_map)    # 获取最小温度
                            max_index = np.argmax(frame_temp_map)     
                            max_coords = np.unravel_index(max_index, frame_temp_map.shape)
                            min_index = np.argmin(frame_temp_map)
                            min_coords = np.unravel_index(min_index, frame_temp_map.shape)
                        except Exception as e:
                            Log.error(f"温度数据处理异常: {e}")
                            center_value = max_value = min_value = 0
                            max_coords = min_coords = (0, 0)

                        # 转换颜色并显示图像
                        frame_img = cv2.cvtColor(frame_img, cv2.COLOR_BGR2RGB)  # 转换成RGB
                        
                        # 绘制屏幕元素
                        spi_lcd.crosshair(frame_img, size[0]//2, size[1]//2, (0,255,0)) # 绘制中心点准心，绿色
                        spi_lcd.crosshair(frame_img, max_coords[1], max_coords[0], (255,0,0)) # 绘制高温准星，红色
                        spi_lcd.crosshair(frame_img, min_coords[1], min_coords[0], (0,0,255)) # 绘制低温准星，蓝色
                        spi_lcd.HUD_data(frame_img, max_value, center_value, min_value, laser, power, 384, 288)  # 绘制计量数据

                        frame_img = cv2.cvtColor(frame_img, cv2.COLOR_RGB2BGR)  # 转换成BGR，opencv默认BGR

                        # 将画面从384x288插值到640宽度
                        target_width = 640
                        target_height = int(288 * (target_width / 384))  # 保持原始宽高比
                        frame_img = cv2.resize(frame_img, (target_width, target_height), 
                                              interpolation=cv2.INTER_LANCZOS4)  # Lanczos插值提供最佳质量
                        
                        # 更新推流尺寸
                        if frame_count == 2:  # 只在开始时打印一次日志
                            Log.info(f"推流分辨率：{target_width}x{target_height}")

                        # 写入管道
                        pipe.stdin.write(frame_img.tobytes())
                        pipe.stdin.flush()  # 确保数据被发送
                        last_frame_time = current_time  # 更新帧时间
                        
                    except BrokenPipeError as error:
                        Log.error(f"管道已断开: {error}")
                        break
                    except Exception as e:
                        Log.error(f"处理视频帧异常: {str(e)}")
                        continue
                
                # 主动管理队列大小
                if q_video.qsize() > 10:
                    excess_frames = q_video.qsize() - 5  # 保留5帧
                    Log.warning(f"视频队列堆积严重({q_video.qsize()}帧)，丢弃{excess_frames}帧")
                    for _ in range(excess_frames):
                        if not q_video.empty():
                            q_video.get()
            
            # ffmpeg进程已退出
            stderr_output = pipe.stderr.read().decode('utf-8', errors='ignore')
            if stderr_output:
                Log.error(f"ffmpeg进程退出，错误信息: {stderr_output}")
                
        except Exception as e:
            Log.error(f"img_net主循环异常: {str(e)}")
        
        # 确保资源被释放
        if 'pipe' in locals() and pipe is not None:
            try:
                pipe.stdin.close()
                pipe.terminate()
                pipe.wait(timeout=3)
            except:
                pass
        
        Log.info("img_net线程将在3秒后重新启动推流...")
        time.sleep(3)  # 短暂延迟后重试

# 进程 获取gps数据
def gps(cfg):
    """
    GPS数据处理逻辑，作为独立线程运行，监控来自协处理器的GPS数据
    
    协处理器已经处理了原始GPS数据，并通过serial_work函数转发至q_gps队列
    此函数仅作为备用处理和监控功能
    """
    from datetime import datetime
    
    # GPS上报间隔
    report_interval = cfg.get('GPS', {}).get('report_interval', 5)  # 默认5秒
    last_report_time = 0
    
    while True:
        current_time = time.time()
        
        # 定期检查GPS状态
        if current_time - last_report_time >= report_interval:
            if not q_gps.empty():
                try:
                    gps_data = q_gps.get(block=False)
                    gps_json = json.loads(gps_data)
                    Log.info(f"GPS状态: 位置={gps_json.get('gps', [0,0])}, 速度={gps_json.get('speed', 0)}km/h")
                    last_report_time = current_time
                except Exception as e:
                    Log.error(f"GPS数据处理错误: {e}")
            else:
                Log.debug("GPS队列为空，等待数据...")
        
        # 检查错误队列
        if q_mqtt_error.empty() != True:
            mqtt_error_data = q_mqtt_error.get()
            Log.error(f"GPS错误: {mqtt_error_data}")
            
        time.sleep(0.1)

# 进程 获取蓝牙数据数据
def ble_sensor(cfg):
    from modules import ble  # 蓝牙模块
    import asyncio
    time.sleep(10)
    device_D = cfg['ble']['device_D']    # 肩灯
    device_C = cfg['ble']['device_C']    # 气体传感器
    device_B = cfg['ble']['device_B']    # 压力表
    device_A = cfg['ble']['device_A']    # 手环
    
    while True:
        asyncio.run(ble.main(device_C,device_B,device_A,device_D))   # 后端进行调用

# 上报数据拼包业务
def sensor_post_json_work(cfg):
    global laser,power,SOS # 全局变量 距离,电量，SOS事件
    # 监听各数据队列获取对应的最新数据
    updata_data = {
        'Distance':0,   # 距离激光测距仪 单位:米
        'HR':0,   # 心率  单位:bpm
        'BP_H':0,   # 血压 收缩压 单位:mmHg
        'BP_L':0,   # 血压 舒张压 单位:mmHg
        'BO':0,   # 血氧浓度  %
        'BT':0,    # 体温 摄氏度
        'air_pressure' : 0,  # 空气压力(psi)
        'air_temperature': 0,    # 空气表温度 摄氏度
        'air_power':0,  # 空气表剩余电量    %
        'air_alarm_1':0, #仪表报警状态
        'air_alarm_2':0, #仪表报警状态
        'air_time_1':0, # 使用时间
        'air_time_2':0, # 剩余时间
        'H2S':0,  # 二氧化硫
        'H2S_alarm':0,  # 二氧化硫警报
        'CO':0,  # 一氧化碳
        'CO_alarm':0,  # 一氧化碳警报
        'CH4':0,  # 甲烷
        'CH4_alarm':0,  # 甲烷警报
        'O2':0,  # 氧气
        'O2_alarm':0,  # 氧气警报
        'falldown':False,   # 跌倒状态
        'SOS':False,  # SOS按键状态
        'power':0  # 电池电量
    }
    
    def repeated_task(updata_data,delay_time= 30) :
        while True:
            q_mqtt_post_ble.put(json.dumps(updata_data))    #
            time.sleep(delay_time)        
        # 创建并启动一个线程来运行定时任务
    task_thread = threading.Thread(target=repeated_task,args = (updata_data,))    # 从从fg中获取时间间隔
    task_thread.daemon = True  # 确保主程序退出时该线程也会随之退出
    task_thread.start()
    
    
    # 循环监听各数据队列,并更新数据
    while True:
        
        # SOS按键状态
        updata_data['SOS'] = SOS
        
        # 检查跌倒状态,给肩灯控制器持续发送
        if updata_data['falldown'] == True:
            if q_beep_light.empty() == True:   # 队列没满就一直发
                q_beep_light.put(updata_data['falldown'])
        
        # 电池电量
        if q_power.empty() != True:
            power = q_power.get()
            updata_data['power'] = power
        
        # 获取跌倒状态
        if q_attitude_io.empty() != True:
            updata_data['falldown'] = q_attitude_io.get()
            # 判断以下跌倒状态,如果为true 转发一个蓝牙任务队列消息
        #从距离队列获取数
        if q_laser.empty()  != True:
            laser = q_laser.get()   # 获取 距离数据
            updata_data['Distance'] = laser
        # 从蓝牙队列获取数据
        if q_ble_data.empty()  != True:  # 如果蓝牙数据队列有数据
            ble_data = q_ble_data.get()  # 获取数据
            ble_data = json.loads(ble_data)  # 解析json
            # 进行判断
            if ble_data['type'] == 10:   #气体传感器
                Log.debug(ble_data['gas_name'])  # 调试信息
                # 判断消息是H2S
                if 'H2S' in ble_data['gas_name']:
                    # 暂时不判断数字倍率
                    updata_data['H2S'] = ble_data['gas_concentration']
                    updata_data['H2S_alarm'] = ble_data['alarm']
                elif 'CO' in ble_data['gas_name']:
                    # 暂时不判断数字倍率
                    updata_data['CO'] = ble_data['gas_concentration']
                    updata_data['CO_alarm'] = ble_data['alarm']
                elif 'CH4' in ble_data['gas_name']:
                    # 暂时不判断数字倍率
                    updata_data['CH4'] = ble_data['gas_concentration']
                    updata_data['CH4_alarm'] = ble_data['alarm']
                elif 'O2' in ble_data['gas_name']:
                    # 暂时不判断数字倍率
                    updata_data['O2'] = ble_data['gas_concentration']   * 0.01    # 单位% 小数点左移2位(说明书要求)
                    updata_data['O2_alarm'] = ble_data['alarm'] 
                else:
                    pass
            elif ble_data['type'] == 20:   # 呼吸器压力标
                updata_data['air_pressure'] = ble_data['air_pressure']
                updata_data['air_temperature'] = ble_data['temp']
                updata_data['air_power'] = ble_data['power']
                updata_data['air_alarm_1'] = ble_data['alarm'][0]
                updata_data['air_alarm_2'] = ble_data['alarm'][1]
                updata_data['air_time_1'] = ble_data['Time'][0]
                updata_data['air_time_2'] = ble_data['Time'][1]
            elif ble_data['type'] == 30:   # 蓝牙手环
                updata_data['HR'] = ble_data['heart_rate']
                updata_data['BP_H'] = ble_data['BP'][0]
                updata_data['BP_L'] = ble_data['BP'][1]
                updata_data['BO'] = ble_data['BO']
                updata_data['BT'] = ble_data['Temp']
            # 组装数据
            
        else:
            time.sleep(0.1)

                  
# MQTT 进程
def thre_mqtt_work(cfg):
    
    import paho.mqtt.client as mqtt
    import ssl
    
    # mqtt 链接参数相关配置
    username = cfg['MQTT']['username']
    broker = cfg['MQTT']['url']
    port = int(cfg['MQTT']['port'])
    username = cfg['MQTT']['username']
    password = cfg['MQTT']['password']
    subscribe_topic = cfg['MQTT']['topic_get']
    publish_topic = cfg['MQTT']['topic_post']
    tls_mode = cfg['MQTT']['tls']
    
    
    subscribe_audio_topic = cfg['MQTT']['topic_audio_get']   # 音频专用topic  接收[本设备]
    publish_audio_topic = cfg['MQTT']['topic_audio_post']   # 音频专用topic  发送[本设备]
    
    error_topic =  cfg['MQTT']['topic_error']
    gpt_topic = cfg['MQTT']['topic_gps']
    
    # 新增4G模块信息日志topic
    log_topic = cfg['MQTT']['topic_log']
    
    device_model_id  = cfg['device']['device_model_id']
    # MQTT 消息处理回调函数
    def on_connect(client, userdata, flags, reason_code, properties):
        Log.info("连接成功，返回码: " + str(reason_code))
        # 订阅主题
        client.subscribe(subscribe_topic)  # 订阅消息主题
        client.subscribe(subscribe_audio_topic)  # 订阅语音消息主题

        
    # 消息回调
    def on_message(client, userdata, msg):
        if  subscribe_audio_topic == msg.topic:   # 如果有接收到音频 推送至指定的队列
            q_play_mqtt_get.put(msg.payload)  
        elif subscribe_topic == msg.topic:    # 普通消息推送至普通消息队列
            q_mqtt_get.put(msg.payload)
            Log.info(msg.payload)
    # 断开回调函数
    def on_disconnect(client, userdata, rc): 
        while  rc != 0:
            try:
                rc = client.reconnect()
                Log.info("mqtt重连")
            except BaseException as error:
                #print("Failed to reconnect to MQTT broker")
                time.sleep(10)
                   

    # 创建 MQTT 客户端
    client_mqtt = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2)
    # 设置用户名和密码
    client_mqtt.username_pw_set(username, password)
    # 启用 TLS/SSL
    if tls_mode == 'true':
        client_mqtt.tls_set(cert_reqs=ssl.CERT_REQUIRED)
    # 连接到 MQTT 代理
    client_mqtt.on_connect = on_connect
    client_mqtt.on_message = on_message
    try:
        client_mqtt.connect(broker, port)
    except Exception as e:
        Log.info(f"连接失败: {e}")
        


    # 独立进程监听MQTT回调
    client_mqtt.loop_start()
    
    while True:
        # 检查4G模块信息日志队列，优先级最高
        if q_mqtt_modem_log.empty() != True:
            modem_log_data = q_mqtt_modem_log.get()
            client_mqtt.publish(log_topic, modem_log_data, qos=0)
            Log.info(f"发送4G模块信息到MQTT: {modem_log_data}")
        #播放器音频
        elif q_play_mqtt_post.empty() != True:
            audio_Data = q_play_mqtt_post.get()
            client_mqtt.publish(publish_audio_topic,audio_Data,qos=0)   # 单独的音频信道推送
            if q_play_mqtt_post.qsize() > 2 :     # 如果队列里面的数据有积压
                q_play_mqtt_post.queue.clear()    # 清空队列
        # 检查GPS队列.如果有数据,就推送
        elif q_gps.empty()  != True:
            gps_data = q_gps.get()
            try:
                gps_data = json.loads(gps_data)
            except TypeError as e:
                Log.error(f"GPS数据格式错误: {e}  {gps_data}")
            else:
                gps_data['username'] = username
                gps_data['device_id'] = device_model_id    # 物模型ID
                #gps_data['type'] ='GPS'
                out_json = json.dumps(gps_data)
                client_mqtt.publish(gpt_topic,out_json,qos=0)
        # 检查蓝牙传感器数据,有就推送    
        elif q_mqtt_post_ble.empty()  != True:
            mqtt_post_data = q_mqtt_post_ble.get()
            mqtt_post_data = json.loads(mqtt_post_data)
            mqtt_post_data['username'] = username
            mqtt_post_data['device_id'] = device_model_id    # 物模型ID
            #mqtt_post_data['type'] ='sensor'
            out_json = json.dumps(mqtt_post_data)
            client_mqtt.publish(publish_topic,out_json,qos=0)
        
        # 错误消息队列检查,并发送到mqtt错误收集主题
        elif q_mqtt_error.empty()  != True:
            mqtt_error_data = q_mqtt_error.get()
            try:
                mqtt_error_data = json.loads(mqtt_error_data)
            except TypeError as e:
                Log.error(f"错误消息格式错误: {e}")
                mqtt_error_data = {'error':str(mqtt_error_data)}
            mqtt_error_data['username'] = username
            #mqtt_error_data['type'] ='error'
            out_json = json.dumps(mqtt_error_data)
            client_mqtt.publish(error_topic,out_json,qos=0)
         # 通用 上行消息队列
        elif q_mqtt_post.empty() != True:
            mqtt_post_data = q_mqtt_post.get() # 获取数据
            out_json = json.dumps(mqtt_post_data)  # 编码json字符串
            client_mqtt.publish(publish_topic,out_json,qos=0)   # 直接推送数据
            Log.info(out_json)
        else:
            time.sleep(0.01)

    
# 串口进程
def  serial_work(cfg):
    from modules import mcu
    
    tty_url = mcu.get_mcu_tty()  # 获取mcu的串口url
    Log.info(tty_url)
    ser = serial.Serial(tty_url,115200,timeout=2)
    while True:
        req = ser.readline()
        if req != b'':
            # O = 20 距离事件, O:10 短按按键事件  O:11 长按按键事件
            Log.debug(req)      #日志
            try:
                json_code = json.loads(req)
                
                if json_code['O'] == 24:     # 跌倒警报消息
                    q_attitude.put(json_code['D'])   # 跌倒数据
                    Log.info(f"收到跌倒数据: {json_code['D']}")
                elif json_code['O'] == 23:    # GPS数据
                    if json_code['D']['valid']:  # GPS数据有效
                        gps_data = {
                            'time': json_code['D']['time'],
                            'gps': [json_code['D']['lat'], json_code['D']['lng']],
                            'speed': json_code['D']['speed'],
                            'course': json_code['D']['course'],
                            'altitude': json_code['D']['alt'],
                            'satellites': json_code['D']['sat']
                        }
                        q_gps.put(json.dumps(gps_data))
                        Log.info(f"收到GPS数据: {gps_data}")
                elif json_code['O'] == 22:        # 电量数据的计算值
                    q_power.put(json_code['D'])   # 计算后的电量数据
                elif json_code['O'] == 21:
                    q_attitude.put(json_code['D'][0])   # 姿态数据
                elif json_code['O'] == 20:
                    q_laser.put(json_code['D'] * 0.01)  # 距离事件,消息推送至激光距离消息队列 数据修正到米
                elif json_code['O'] == 10:   # 短按事件
                    Log.info(json_code)     #日志
                    IO = json_code['IO']
                    if IO == 0:             # 开机按键
                        #pass
                        #q_button_0.put(0)
                        q_button_2.put(0)   # 功能键(切换热成像功能) 热成像队列
                    elif IO == 1:           # 任务确认键
                        q_button_1.put(0)
                        q_play_wav.put(5)   # 按键确认音频
                    elif IO == 2:   #开机键业务           
                        pass
                    elif IO == 3:          #SOS事件
                        q_button_3.put(0)
                elif json_code['O'] == 11:   # 长按事件
                    Log.info(json_code)    #日志
                    IO = json_code['IO']
                    if IO == 0:
                        q_button_0.put(1)
                    elif IO == 1:
                        q_button_1.put(1)   
                    elif IO == 2:
                        q_button_2.put(1)
                    elif IO == 3:
                        q_button_3.put(1)
            except json.JSONDecodeError as e:
                Log.error(f"JSON解析错误: {e}, 数据: {req}")
            except Exception as e:
                Log.error(f"处理数据错误: {e}, 数据: {req}")
        time.sleep(0.02)

# 交互逻辑处理器

#首先 检查按键队列有任务,再检查mqtt来源任务不为空. 满足条件则发送应答消息(送到mqtt),然后把全局变量至None
def button_confirmation_work(cfg):
    
    global q_msn_cammand,SOS  # 使用全局变量
    while True:
        if q_button_0.empty()  != True:  # 如果按键0的队列不空 
            pass
        if q_button_1.empty()  != True:  # 如果按键1的队列不空    [暂定功能是确认功能键]  
            button_type = q_button_1.get()  # 获取按键按下类型
            # 区分短按还是长按业务
            if button_type == 0:  # 短按
                # 判断 q_msn_cammand是否为None
                Log.info(q_msn_cammand) 
                if q_msn_cammand != None:
                    # 正常响应业务 
                    # 像mqtt应答消息队列发送回送消息
                    q_mqtt_post.put(q_msn_cammand)
                    # 处理完成后清空消息缓存
                    q_msn_cammand = None
                
                Log.info('确认响应') 
            elif button_type == 1:
                pass
        if q_button_2.empty()  != True:  # 如果按键2的队列不空   [暂定功能是伪色切换键]
            button_type = q_button_2.get()  # 获取按键按下类型
            camear_config.cfg_color()   
        if q_button_3.empty()  != True:  # 如果按键0的队列不空
            button_type = q_button_3.get()  # 获取按键按下类型
            if button_type == 0:  # 短按
                # 3号键短按是用户定义的SOS事件
                SOS = True  # 设置SOS状态为True
                Log.info('SOS短按触发')
            elif button_type == 1:  # 长按
                # 3号键长按是用户定义的SOS事件
                SOS = False  # 设置SOS状态为True
                Log.info('SOS长按关闭')
                
                
                
        time.sleep(0.02)    

# 传入消息处理人维护
def get_msn_work(cfg):
    global q_msn_cammand
    while True:
        if q_mqtt_get.empty() != True:
            mqtt_get_data = q_mqtt_get.get()
            Log.info(mqtt_get_data)
            # 解码消息
            try:
                data  = json.loads(mqtt_get_data)
            except BaseException as error:
                Log.error(error)
            else:  
                # 处理服务器下发的消息 判断如果是 type":"controlNotification" ,则是指令消息 
                if  data['type'] == 'controlNotification':
                    q_msn_cammand = data # 解码后的消息直接存入
                    # 需要给播放器队列,传递个播放消息
                    q_play_wav.put(data['value']) 
                    Log.debug(data)
        else:
            time.sleep(0.02)


# 录音进程
def audio_input_work(cfg):

    audio.input_init()   # 输入初始化
    while True:
        audio_data = audio.record()  # 录制音频
        q_play_mqtt_post.put(audio_data)  # 录制的音频推送至队列

# 音频播放进程
def audio_output_work(cfg):
    import wave
    
    wf01 = wave.open('wav/jc.wav', 'rb')   # 进场
    wf02 = wave.open('wav/xc.wav', 'rb')   # 巡查
    wf03 = wave.open('wav/zt.wav', 'rb')   # 暂停
    wf04 = wave.open('wav/ct.wav', 'rb')   # 撤退
    wf05 = wave.open('wav/ding.wav', 'rb')   # ding...
    
    audio.output_init()  # 输出初始化
    
    
    while True:
        if q_play_mqtt_get.empty() != True:    # 如果队列里有入站的音频,就获取播放
            audio_data = q_play_mqtt_get.get()
            audio.play(audio_data)
            
            #防止队列数据积压
            if q_play_mqtt_get.qsize() > 2 :     # 如果队列里面的数据有积压
                q_play_mqtt_get.queue.clear()    # 清空队列
                
        elif q_play_wav.empty() != True:   # 如果有提示音消息
            type_data = q_play_wav.get()
            Log.info(type_data)
            # 分析数据值,选择对应的提示音播放
            if type_data == 1:
                data = wf01.readframes(44100//4)  # 读取数据
                while data != b'':  # 播放 
                    audio.play_stream.write(data)
                    data = wf01.readframes(44100 // 4)
                wf01.rewind()
            elif type_data == 2:
                data = wf02.readframes(44100//4)  # 读取数据
                while data != b'':  # 播放 
                    audio.play_stream.write(data)
                    data = wf02.readframes(44100 // 4)
                wf02.rewind()
            elif type_data == 3:
                data = wf03.readframes(44100//4)  # 读取数据
                while data != b'':  # 播放 
                    audio.play_stream.write(data)
                    data = wf03.readframes(44100 // 4)
                wf03.rewind()
            elif type_data == 4:
                data = wf04.readframes(44100//4)  # 读取数据
                while data != b'':  # 播放 
                    audio.play_stream.write(data)
                    data = wf04.readframes(44100 // 4)
                wf04.rewind()
            elif type_data == 5:
                data = wf05.readframes(44100//4)  # 读取数据
                while data != b'':  # 播放 
                    audio.play_stream.write(data)
                    data = wf05.readframes(44100 // 4) 
                wf05.rewind()
        else:
            time.sleep(0.01)


# 跌倒检测算法进程
def attitude_work(cfg):
    fall_status = False          # 当前跌倒状态
    fall_count = 0               # 连续跌倒信号计数
    fall_count_threshold = 30    # 触发跌倒报警的连续信号阈值
    
    last_fall_time = time.time() # 上次收到跌倒信号的时间
    recover_timeout = 5          # 自动恢复超时时间(秒)
    
    while True:
        if q_attitude.empty() != True:    # 如果队列里有传感器数据
            fall_data = q_attitude.get()
            # 协处理器已经判断了跌倒状态，这里直接处理
            if isinstance(fall_data, dict) and 'fall' in fall_data:
                last_fall_time = time.time()  # 更新最后接收时间
                
                if fall_data['fall'] == True:  # 协处理器检测到跌倒状态
                    fall_count += 1  # 增加跌倒计数
                    Log.debug(f"检测到跌倒信号，当前计数: {fall_count}/{fall_count_threshold}")
                    
                    # 检查是否达到连续信号阈值
                    if fall_count >= fall_count_threshold and not fall_status:
                        fall_status = True
                        q_attitude_io.put(fall_status)  # 更新跌倒状态至队列
                        Log.info(f"跌倒警报触发：连续接收{fall_count}个跌倒信号")

                else:  # 协处理器报告未跌倒
                    # 重置跌倒计数
                    if fall_count > 0:
                        Log.debug(f"收到非跌倒信号，重置计数（之前为{fall_count}）")
                        fall_count = 0
                    
                    # 如果当前处于跌倒状态，则恢复
                    if fall_status:
                        fall_status = False
                        q_attitude_io.put(fall_status)  # 更新恢复状态至队列
                        Log.info("跌倒状态已恢复（收到非跌倒信号）")
        else:
            # 检查超时恢复
            if time.time() - last_fall_time > recover_timeout:
                # 超过5秒未收到跌倒信号，重置计数
                if fall_count > 0:
                    Log.debug(f"超过{recover_timeout}秒未收到跌倒信号，重置计数（之前为{fall_count}）")
                    fall_count = 0
                
                # 如果当前处于跌倒状态，则恢复
                if fall_status:
                    fall_status = False
                    q_attitude_io.put(fall_status)  # 更新恢复状态至队列
                    Log.info(f"跌倒状态已恢复（超过{recover_timeout}秒未收到跌倒信号）")
                    
            time.sleep(0.1)
            continue
        
        time.sleep(0.01)  # 避免CPU占用过高


# 4G AT指令通信进程
def modem_at_work(cfg):
    """
    通过AT指令获取4G模块的相关数据：IMEI, ICCID, UICC(IMSI)、信号强度和基站小区信息
    每30秒采集一次信号强度和基站小区信息并合并上报
    """
    global modem_imei, modem_iccid, modem_uicc, modem_signal, modem_servingcell
    
    # 获取设备用户名
    username = cfg['MQTT']['username']
    device_model_id = cfg['device']['device_model_id']
    
    # 定义AT指令
    at_imei = "AT+CGSN\r\n"
    at_iccid = "AT+QCCID\r\n"
    at_uicc = "AT+CIMI\r\n"
    at_signal = "AT+CSQ\r\n"
    at_servingcell = 'AT+QENG="servingcell"\r\n'
    
    try:
        # 打开4G模块的AT通信串口
        ser = serial.Serial('/dev/ttyACM0', 115200, timeout=2)
        Log.info('4G模块AT串口已打开')
        
        # 首先获取设备基本信息
        ser.write(at_imei.encode())
        time.sleep(0.5)
        resp = ser.read_all().decode('utf-8', errors='ignore').strip()
        if '+CGSN:' in resp:
            modem_imei = resp.split('\r\n')[1].strip()
        else:
            modem_imei = resp.replace('OK', '').replace('AT+CGSN', '').strip()
        Log.info(f'4G模块IMEI: {modem_imei}')
        
        ser.write(at_iccid.encode())
        time.sleep(0.5)
        resp = ser.read_all().decode('utf-8', errors='ignore').strip()
        if '+QCCID:' in resp:
            modem_iccid = resp.split('+QCCID:')[1].split('\r\n')[0].strip()
        Log.info(f'4G模块ICCID: {modem_iccid}')
        
        ser.write(at_uicc.encode())
        time.sleep(0.5)
        resp = ser.read_all().decode('utf-8', errors='ignore').strip()
        modem_uicc = resp.replace('OK', '').replace('AT+CIMI', '').strip()
        Log.info(f'4G模块UICC: {modem_uicc}')
        
        # 发送初始的4G模块信息到MQTT队列
        modem_info = {
            'type': '4G_modem_info',
            'imei': modem_imei,
            'iccid': modem_iccid,
            'uicc': modem_uicc,
            'username': username,
            'device_id': device_model_id
        }
        q_mqtt_modem_log.put(json.dumps(modem_info))
        
        while True:
            modem_network_info = {
                'type': '4G_network_info',
                'imei': modem_imei,
                'username': username,
                'device_id': device_model_id,
                'signal': '',
                'cell_data': {}
            }
            
            # 获取信号强度
            ser.write(at_signal.encode())
            time.sleep(0.5)
            resp = ser.read_all().decode('utf-8', errors='ignore').strip()
            if '+CSQ:' in resp:
                modem_signal = resp.split('+CSQ:')[1].split('\r\n')[0].strip()
                Log.info(f'4G模块信号强度: {modem_signal}')
                modem_network_info['signal'] = modem_signal
            
            # 获取基站小区信息
            ser.write(at_servingcell.encode())
            time.sleep(0.5)
            resp = ser.read_all().decode('utf-8', errors='ignore').strip()
            
            if '+QENG:' in resp:
                # 解析基站小区信息
                try:
                    cell_info = resp.split('+QENG:')[1].split('\r\n')[0].strip()
                    parts = cell_info.split(',')
                    
                    # 移除多余的引号
                    for i in range(len(parts)):
                        parts[i] = parts[i].strip('"')
                    
                    # 至少有LTE基本信息的情况下处理
                    if len(parts) >= 4 and parts[2] == "LTE":
                        modem_servingcell = {
                            "type": parts[2],  # "LTE"
                            "is_tdd": parts[3],  # "FDD" 或 "TDD"
                            "mcc": parts[4] if len(parts) > 4 else "",
                            "mnc": parts[5] if len(parts) > 5 else "",
                            "cellid": parts[6] if len(parts) > 6 else "",
                            "pcid": parts[7] if len(parts) > 7 else "",
                            "earfcn": parts[8] if len(parts) > 8 else "",
                            "freq_band": parts[9] if len(parts) > 9 else "",
                            "ul_bandwidth": parts[10] if len(parts) > 10 else "",
                            "dl_bandwidth": parts[11] if len(parts) > 11 else "",
                            "tac": parts[12] if len(parts) > 12 else "",
                            "rsrp": parts[13] if len(parts) > 13 else "",  # 参考信号接收功率
                            "rsrq": parts[14] if len(parts) > 14 else "",  # 参考信号接收质量
                            "rssi": parts[15] if len(parts) > 15 else "",  # 接收信号强度
                            "sinr": parts[16] if len(parts) > 16 else "",  # 信噪比
                            "srxlev": parts[17] if len(parts) > 17 else ""  # 小区接收电平
                        }
                        Log.info(f'4G模块基站小区信息: MCC={modem_servingcell["mcc"]}, '
                                f'MNC={modem_servingcell["mnc"]}, '
                                f'CellID={modem_servingcell["cellid"]}, '
                                f'RSRP={modem_servingcell["rsrp"]}, '
                                f'RSRQ={modem_servingcell["rsrq"]}, '
                                f'SINR={modem_servingcell["sinr"]}')
                        
                        modem_network_info['cell_data'] = modem_servingcell
                except Exception as e:
                    Log.error(f'解析基站小区信息出错: {e}')
            
            # 发送合并后的4G网络信息到MQTT队列
            q_mqtt_modem_log.put(json.dumps(modem_network_info))
            Log.info('已合并上报4G网络信息(信号强度和基站小区信息)')
            
            time.sleep(30)  # 每30秒查询并上报一次
            
    except Exception as e:
        Log.error(f'4G模块AT通信错误: {e}')
    finally:
        if 'ser' in locals() and ser.is_open:
            ser.close()
            Log.info('4G模块AT串口已关闭')


if __name__ == "__main__":
    #import os
    #os.system('echo raspberry | sudo ip route del  default dev eth1') 
    import os,time
    os.system('echo raspberry | sudo dhclient')
    import yaml
    # 获取配置信息
    # 获取配置文件内容
    yaml_file_url = '/home/<USER>/main.yaml'
    ymal_file = open(yaml_file_url,'r',encoding='utf-8')
    cfg = yaml.load(ymal_file,Loader=yaml.FullLoader)
    ymal_file.close()
    
    # 线程控制
    t_camear_live = threading.Thread(target=camear_live,args = (cfg,))  # 创建一个线程 专门获取可见光
    t_camear_get = threading.Thread(target=camear_get,args = (cfg,))  # 创建一个线程 专门获取热成像画面
    t_lcd_work = threading.Thread(target=lcd_work,args = (cfg,))  # 创建一个线程   显示热成像画面到LCD屏幕
    t_img_net = threading.Thread(target=img_net,args =(cfg,))  # 创建一个线程    推送热成像画面到阿里云
    
    t_serial_work = threading.Thread(target=serial_work,args = (cfg,))     # 串口业务
    t_button_confirmation_work = threading.Thread(target=button_confirmation_work,args = (cfg,))   # 按键任务调度
    
    t_sensor_post_json_work = threading.Thread(target=sensor_post_json_work,args =(cfg,))  # 创建一个线程,单纯推送可见光画面到阿里云
    t_ble_sensor = threading.Thread(target=ble_sensor,args = (cfg,))  # 创建一个线程    获取数据到消息队列
    t_thre_mqtt_work = threading.Thread(target=thre_mqtt_work,args = (cfg,))  # 创建一个线程    mqtt业务
    t_gps = threading.Thread(target=gps,args = (cfg,))  # 创建一个线程    监控GPS数据
    
    t_audio_input_work = threading.Thread(target=audio_input_work,args = (cfg,))  # 音频业务  录制
    t_audio_output_work = threading.Thread(target=audio_output_work,args = (cfg,))  # 音频业务  播放 
    
    t_get_msn_work = threading.Thread(target=get_msn_work,args = (cfg,))  # 信息交互业务
    
    t_attitude_work = threading.Thread(target=attitude_work,args = (cfg,))  # 信息交互业务
    
    # 4G模块AT通信线程
    t_modem_at_work = threading.Thread(target=modem_at_work,args = (cfg,))  # 4G模块AT通信线程
    
    # 队列监控线程
    t_queue_monitor = threading.Thread(target=queue_monitor,args = (cfg,))  # 队列监控线程
    
    # 可见光摄像头状态标志
    cam_live_status = {
        "available": True,      # 摄像头是否可用
        "restart_count": 0,     # 重启计数
        "last_check_time": 0,   # 上次检查时间
        "disabled": False       # 是否已禁用重启
    }
        
    # 启动线程
    t_camear_live.start()  # 启动可见光推流线程
    t_camear_get.start()  # 启动热成像获取线程
    t_lcd_work.start()    # 启动LCD显示线程
    t_img_net.start()     # 启动热成像推流线程
    
    t_ble_sensor.start()       # 启动蓝牙传感器线程
    t_thre_mqtt_work.start()   # 启动MQTT通信线程
    t_sensor_post_json_work.start()  # 启动传感器数据处理线程
    t_gps.start()              # 启动GPS监控线程
    
    t_serial_work.start()      # 启动串口业务线程
    t_button_confirmation_work.start()  # 启动按键任务调度线程

    t_audio_input_work.start()  # 启动音频录制线程
    t_audio_output_work.start()  # 启动音频播放线程
    
    t_get_msn_work.start()    # 启动信息交互业务线程
    t_attitude_work.start()    # 启动姿态监测线程
    
    t_modem_at_work.start()    # 启动4G模块AT通信线程
    t_queue_monitor.start()    # 启动队列监控线程
        
    # 线程监控循环
    while True:
        time.sleep(1)
        
        # 监控关键线程状态并在需要时重启
        thread_checks = [
            (t_img_net, img_net, "热成像推流"),
            (t_thre_mqtt_work, thre_mqtt_work, "MQTT通信"),
            (t_gps, gps, "GPS监控"),
            (t_modem_at_work, modem_at_work, "4G模块通信"),
            (t_queue_monitor, queue_monitor, "队列监控")
        ]
        
        # 特殊处理可见光推流线程
        if not t_camear_live.is_alive() and not cam_live_status["disabled"]:
            # 增加重启次数
            cam_live_status["restart_count"] += 1
            current_time = time.time()
            
            # 检查是否短时间内多次重启
            if current_time - cam_live_status["last_check_time"] > 60:
                # 如果超过60秒，重置计数
                cam_live_status["restart_count"] = 1
                cam_live_status["last_check_time"] = current_time
            
            # 检查摄像头是否可用
            cam_id = 'USB HD Camera'
            video_url = cam_num.get_cam_img(cam_id)
            
            if not video_url:
                # 摄像头不可用
                if cam_live_status["restart_count"] >= 3:
                    Log.error("可见光摄像头不可用，且短时间内多次尝试失败，停止重启此线程")
                    cam_live_status["disabled"] = True
                    continue
                else:
                    Log.warning(f"可见光摄像头不可用，等待30秒后重试 (尝试 {cam_live_status['restart_count']}/3)")
                    time.sleep(30)  # 等待30秒后再尝试
            
            # 尝试重启线程
            Log.info("重启可见光推流线程...")
            t_camear_live = threading.Thread(target=camear_live, args=(cfg,))
            t_camear_live.start()
            
            # 如果重启成功且摄像头可用，更新状态
            if video_url:
                cam_live_status["available"] = True
                
        # 每5分钟检查一次被禁用的摄像头是否已连接
        if cam_live_status["disabled"] and time.time() % 300 < 1:
            cam_id = 'USB HD Camera'
            if cam_num.get_cam_img(cam_id):
                Log.info("检测到可见光摄像头已连接，重新启用推流线程")
                cam_live_status["disabled"] = False
                cam_live_status["restart_count"] = 0
                t_camear_live = threading.Thread(target=camear_live, args=(cfg,))
                t_camear_live.start()
        
        # 处理其他线程
        for thread, func, name in thread_checks:
            if not thread.is_alive():
                Log.error(f"{name}线程已停止，正在重启...")
                new_thread = threading.Thread(target=func, args=(cfg,))
                new_thread.start()
                Log.info(f"{name}线程已重启")
                
                # 更新对应的线程变量
                if name == "热成像推流":
                    t_img_net = new_thread
                elif name == "MQTT通信":
                    t_thre_mqtt_work = new_thread
                elif name == "GPS监控":
                    t_gps = new_thread
                elif name == "4G模块通信":
                    t_modem_at_work = new_thread
                elif name == "队列监控":
                    t_queue_monitor = new_thread
                    
                # 一次只处理一个线程重启，避免系统负载过大
                break
