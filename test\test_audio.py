import pyaudio
import numpy as np

from scipy.signal import resample

audio = pyaudio.PyAudio()

p = pyaudio.PyAudio()



for i in range(p.get_device_count()):
    info = p.get_device_info_by_index(i)
    print(f"Device {i}: {info['name']}")

# 选择合适的设备索引
device_index = 1  # 替换为实际设备索引
In_RATE = 44100
Out_RATE = 1024*6
read_stream = audio.open(format=pyaudio.paInt16, channels=1, rate=In_RATE, input=True,  frames_per_buffer=In_RATE // 4,input_device_index=device_index)
play_stream = audio.open(format=pyaudio.paInt16, channels=1, rate= Out_RATE,  output=True, frames_per_buffer= Out_RATE // 4,input_device_index=device_index)




while True:
    audio_data = read_stream.read((In_RATE // 4),exception_on_overflow = False)
    # 将音频数据转换为numpy数组
    audio_data = np.frombuffer(audio_data, dtype=np.int16)
    # 重采样，将采样率从44100降至8000
    num_samples = int(len(audio_data) * Out_RATE / In_RATE)
    resampled_audio_data = resample(audio_data, num_samples)
   
    # 写入播放流
    play_stream.write(resampled_audio_data.astype(np.int16).tobytes()) # 写入播放流