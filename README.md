git config --global user.email "<EMAIL>"  
git config --global user.name "chengle<PERSON>"  




![USB重置功能](https://github.com/mvp/uhubctl)
```shell
sudo apt-get install libusb-1.0-0-dev
git clone https://github.com/mvp/uhubctl
cd uhubctl
make
sudo make install
```


# 经过确认的安装依赖环境
``` shell
sudo apt install python3-pip  
pip install pyserial --break-system-packages     # 串口库
pip install bleak --break-system-packages        # 蓝牙库
pip install paho-mqtt --break-system-packages    # MQTT库
pip install PyYAML  --break-system-packages      # 配置文件库
# pip install ffmpeg-python  --break-system-packages  
sudo apt install  ffmpeg                            # 视频处理
sudo apt install python3-pyaudio      # 音频处理
pip install scipy  --break-system-packages      # 信号处理库
pip install wave --break-system-packages        # wav文件
```


OpenCV部分  
``` shell
sudo apt update
sudo apt install -y libgl1
pip install opencv-python  --break-system-packages
#sudo apt install -y python3-opencv
```

热成像配置,(伪彩切换)  
``` shell
pip install pyusb --break-system-packages    # usb
pip install pyudev  --break-system-packages # 用于识别设备

```


SPI驱动部分  
``` shell
sudo apt install swig python3-dev
sudo apt install python3-setuptools
cd ~
wget https://github.com/joan2937/lg/archive/master.zip
unzip master.zip
cd lg-master
sudo make install 

需要开启SPI   [特别注意]  raspi-config
```




## 禁用WiFi
``` shell
sudo nano /boot/firmware/config.txt

末尾添加

dtoverlay=disable-wifi
```


## 音频部分 

``` shell
pip install scipy --break-system-packages

```

``` shell
sudo raspi-config

1 System Options
S2 AUDIO
配置默认声卡


alsamixer     # 命令行配置音量
```


## 处理USB权限问题


```config  /etc/udev/rules.d/99-camear.rules
sudo nano /etc/udev/rules.d/99-camear.rules

SUBSYSTEM=="usb", ATTR{idVendor}=="0bda", ATTR{idProduct}=="5830", MODE="0666"
```


## 解决4G DNS的问题

```bash
sudo apt install systemd-resolved
```
然后修改配置文件  

```bash
sudo nano /etc/systemd/resolved.conf
```

添加以下内容  
```config
[Resolve]
DNS=*******
```

调整音量

``` bash 
# amixer sset Speaker 37  设置喇叭音量
# amixer sset Mic 35  # 麦克风音量
```



## 开机启动

```bash
 su - pi -c 'python3 /home/<USER>/device_code/start.py &'
```




 topic_audio_get = /XF/voice/Flat/60d288936bca467f81ec70f197ad8f69
 topic_audio_post = /XF/voice/equipment/60d288936bca467f81ec70f197ad8f69




 ## 启动参数

sudo systemctl stop python-work-run.service   
sudo systemctl start python-work-run.service



USB 调试端口启动
'''bash
sudo nano /boot/firmware/config.txt

[cm4]
enable_uart=1
dtoverlay=disable-wifi


[cm5]
dtparam=uart0_console
'''