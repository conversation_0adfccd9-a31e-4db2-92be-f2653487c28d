# 4G模块MQTT通信协议说明

## 1. 概述

本文档描述4G CAT4模块通过MQTT协议上报数据的消息结构和字段说明。系统通过AT指令与4G模块通信，获取IMEI、ICCID、UICC号、信号强度和基站小区信息，并定期通过MQTT发送到服务器。

## 2. 通信参数

- **MQTT主题**: `topic_log` (从配置文件main.yaml获取)
- **QoS等级**: 0
- **数据格式**: JSON字符串

## 3. 消息类型

系统发送两种类型的4G模块信息：

1. 设备基本信息 - 仅系统启动时发送一次
2. 网络信息 - 定期发送，包含信号强度和基站小区信息

## 4. 消息结构

### 4.1 设备基本信息

该消息在系统启动时发送，包含4G模块的基本标识信息。

```json
{
    "type": "4G_modem_info",
    "imei": "设备IMEI号",
    "iccid": "SIM卡ICCID号",
    "uicc": "UICC(IMSI)号",
    "username": "设备用户名",
    "device_id": "设备模型ID"
}
```

#### 字段说明：

| 字段名     | 类型   | 描述                            | 获取AT指令 |
|------------|--------|--------------------------------|------------|
| type       | 字符串 | 消息类型，固定为"4G_modem_info" | -          |
| imei       | 字符串 | 设备的国际移动设备识别码        | AT+CGSN    |
| iccid      | 字符串 | SIM卡的集成电路卡识别码         | AT+QCCID   |
| uicc       | 字符串 | 通用集成电路卡(IMSI)号码        | AT+CIMI    |
| username   | 字符串 | 设备用户名                      | 配置文件   |
| device_id  | 字符串 | 设备模型ID                      | 配置文件   |

### 4.2 网络信息（合并后的信号和基站信息）

该消息每30秒发送一次，同时包含4G模块的信号强度信息和基站小区信息。

```json
{
    "type": "4G_network_info",
    "imei": "设备IMEI号",
    "username": "设备用户名",
    "device_id": "设备模型ID",
    "signal": "信号强度信息",
    "cell_data": {
        "type": "网络类型",
        "is_tdd": "TDD/FDD模式",
        "mcc": "移动国家码",
        "mnc": "移动网络码",
        "cellid": "小区ID",
        "pcid": "物理小区ID",
        "earfcn": "E-UTRA绝对射频信道号",
        "freq_band": "频段指示",
        "ul_bandwidth": "上行带宽",
        "dl_bandwidth": "下行带宽",
        "tac": "跟踪区域码",
        "rsrp": "参考信号接收功率",
        "rsrq": "参考信号接收质量",
        "rssi": "接收信号强度指示",
        "sinr": "信噪比",
        "srxlev": "小区接收电平"
    }
}
```

#### 字段说明：

| 字段名               | 类型    | 描述                                  | 获取AT指令                 |
|----------------------|---------|--------------------------------------|----------------------------|
| type                 | 字符串  | 消息类型，固定为"4G_network_info"     | -                          |
| imei                 | 字符串  | 设备的国际移动设备识别码               | -                          |
| username             | 字符串  | 设备用户名                           | 配置文件                    |
| device_id            | 字符串  | 设备模型ID                           | 配置文件                    |
| signal               | 字符串  | 信号强度值，格式为"rssi,ber"          | AT+CSQ                     |
| cell_data            | 对象    | 包含基站小区详细信息的对象             | AT+QENG="servingcell"     |

**signal字段信号强度解释**：
- RSSI (接收信号强度指示): 
  - 0: -113 dBm或更低 (极差)
  - 1-30: -111 到 -53 dBm (值越大信号越好)
  - 31: -51 dBm或更高 (极好)
  - 99: 未知或不可检测
- BER (误码率): 当前不使用

**cell_data对象字段说明**：

| 字段名               | 类型    | 描述                                  | 来源                          |
|----------------------|---------|--------------------------------------|-------------------------------|
| type                 | 字符串  | 网络类型，例如"LTE"                   | +QENG响应第3个字段            |
| is_tdd               | 字符串  | 时分双工/频分双工模式，"TDD"或"FDD"   | +QENG响应第4个字段            |
| mcc                  | 字符串  | 移动国家码(如中国460)                 | +QENG响应第5个字段            |
| mnc                  | 字符串  | 移动网络码(如中国移动00/02/07)        | +QENG响应第6个字段            |
| cellid               | 字符串  | 小区ID                               | +QENG响应第7个字段            |
| pcid                 | 字符串  | 物理小区ID                           | +QENG响应第8个字段            |
| earfcn               | 字符串  | E-UTRA绝对射频信道号                  | +QENG响应第9个字段            |
| freq_band            | 字符串  | 频段指示                             | +QENG响应第10个字段           |
| ul_bandwidth         | 字符串  | 上行带宽                             | +QENG响应第11个字段           |
| dl_bandwidth         | 字符串  | 下行带宽                             | +QENG响应第12个字段           |
| tac                  | 字符串  | 跟踪区域码                           | +QENG响应第13个字段           |
| rsrp                 | 字符串  | 参考信号接收功率，单位dBm             | +QENG响应第14个字段           |
| rsrq                 | 字符串  | 参考信号接收质量，单位dB              | +QENG响应第15个字段           |
| rssi                 | 字符串  | 接收信号强度指示，单位dBm             | +QENG响应第16个字段           |
| sinr                 | 字符串  | 信噪比，单位dB                       | +QENG响应第17个字段           |
| srxlev               | 字符串  | 小区接收电平                         | +QENG响应第18个字段           |

**信号质量指标解释**：
- RSRP (参考信号接收功率): 
  - 优: > -80dBm
  - 良: -80dBm ~ -90dBm
  - 中: -90dBm ~ -100dBm
  - 差: < -100dBm
- RSRQ (参考信号接收质量): 
  - 优: > -10dB
  - 良: -10dB ~ -15dB
  - 中: -15dB ~ -20dB
  - 差: < -20dB
- SINR (信噪比):
  - 优: > 20dB
  - 良: 13dB ~ 20dB
  - 中: 0dB ~ 13dB
  - 差: < 0dB

## 5. 通信时序

1. 系统启动时发送一次设备基本信息(IMEI, ICCID, UICC)
2. 之后每30秒发送一次合并后的网络信息(包含信号强度和基站小区信息)

## 6. 错误处理

当出现通信错误时:
1. 系统会记录错误日志
2. 尝试自动重新连接4G模块
3. 如线程异常退出，主线程监控会自动重启通信线程