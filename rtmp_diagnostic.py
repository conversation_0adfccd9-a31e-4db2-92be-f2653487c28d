#!/usr/bin/env python3
"""
RTMP推流诊断工具
用于诊断RTMP连接问题和网络状况
"""

import socket
import time
import subprocess
import urllib.parse
import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
Log = logging.getLogger(__name__)

def test_network_connectivity():
    """测试基本网络连接"""
    Log.info("=== 网络连接测试 ===")
    
    # 测试DNS解析
    test_hosts = [
        "*******",           # Google DNS
        "***************",   # 114 DNS
        "baidu.com",         # 百度
        "push.affppe.com"    # RTMP服务器
    ]
    
    for host in test_hosts:
        try:
            result = subprocess.run(['ping', '-c', '3', host], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                Log.info(f"✓ {host} - 连接正常")
            else:
                Log.error(f"✗ {host} - 连接失败")
        except Exception as e:
            Log.error(f"✗ {host} - 测试异常: {e}")

def test_rtmp_server(rtmp_url):
    """测试RTMP服务器连接"""
    Log.info("=== RTMP服务器测试 ===")
    
    try:
        # 解析RTMP URL
        parsed_url = urllib.parse.urlparse(rtmp_url)
        server_host = parsed_url.hostname
        server_port = parsed_url.port or 1935
        
        Log.info(f"RTMP服务器: {server_host}:{server_port}")
        Log.info(f"完整URL: {rtmp_url}")
        
        # TCP连接测试
        for attempt in range(3):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                start_time = time.time()
                result = sock.connect_ex((server_host, server_port))
                connect_time = time.time() - start_time
                sock.close()
                
                if result == 0:
                    Log.info(f"✓ 尝试 {attempt+1}: TCP连接成功 ({connect_time:.2f}s)")
                    return True
                else:
                    Log.error(f"✗ 尝试 {attempt+1}: TCP连接失败 (错误码: {result})")
            except Exception as e:
                Log.error(f"✗ 尝试 {attempt+1}: 连接异常: {e}")
            
            if attempt < 2:
                time.sleep(2)
        
        return False
        
    except Exception as e:
        Log.error(f"RTMP服务器测试失败: {e}")
        return False

def test_ffmpeg_rtmp(rtmp_url):
    """测试FFmpeg RTMP推流"""
    Log.info("=== FFmpeg RTMP推流测试 ===")
    
    try:
        # 创建测试视频流
        command = [
            'ffmpeg',
            '-f', 'lavfi',
            '-i', 'testsrc=duration=10:size=320x240:rate=1',
            '-c:v', 'h264',
            '-preset', 'ultrafast',
            '-tune', 'zerolatency',
            '-profile:v', 'baseline',
            '-level', '3.0',
            '-r', '1',
            '-b:v', '100k',
            '-maxrate', '150k',
            '-bufsize', '300k',
            '-g', '2',
            '-rtmp_live', 'live',
            '-f', 'flv',
            '-t', '10',  # 只推流10秒
            rtmp_url
        ]
        
        Log.info("启动FFmpeg测试推流...")
        Log.info(f"命令: {' '.join(command)}")
        
        start_time = time.time()
        result = subprocess.run(command, capture_output=True, text=True, timeout=30)
        duration = time.time() - start_time
        
        Log.info(f"FFmpeg执行时间: {duration:.2f}s")
        Log.info(f"退出码: {result.returncode}")
        
        if result.stdout:
            Log.info(f"标准输出:\n{result.stdout}")
        
        if result.stderr:
            Log.info(f"错误输出:\n{result.stderr}")
        
        if result.returncode == 0:
            Log.info("✓ FFmpeg推流测试成功")
            return True
        else:
            Log.error("✗ FFmpeg推流测试失败")
            
            # 分析常见错误
            stderr = result.stderr.lower()
            if "connection refused" in stderr:
                Log.error("  原因: 服务器拒绝连接")
            elif "broken pipe" in stderr:
                Log.error("  原因: 服务器断开连接")
            elif "authentication failed" in stderr or "403" in stderr:
                Log.error("  原因: 认证失败，检查推流密钥")
            elif "network is unreachable" in stderr:
                Log.error("  原因: 网络不可达")
            elif "timeout" in stderr:
                Log.error("  原因: 连接超时")
            else:
                Log.error("  原因: 未知错误")
            
            return False
            
    except subprocess.TimeoutExpired:
        Log.error("✗ FFmpeg推流测试超时")
        return False
    except Exception as e:
        Log.error(f"✗ FFmpeg推流测试异常: {e}")
        return False

def test_bandwidth():
    """测试网络带宽"""
    Log.info("=== 网络带宽测试 ===")
    
    try:
        # 简单的下载测试
        import urllib.request
        import time
        
        test_url = "http://speedtest.tele2.net/1MB.zip"
        Log.info(f"下载测试文件: {test_url}")
        
        start_time = time.time()
        with urllib.request.urlopen(test_url, timeout=30) as response:
            data = response.read()
        download_time = time.time() - start_time
        
        file_size_mb = len(data) / (1024 * 1024)
        speed_mbps = (file_size_mb * 8) / download_time
        
        Log.info(f"下载大小: {file_size_mb:.2f} MB")
        Log.info(f"下载时间: {download_time:.2f} 秒")
        Log.info(f"下载速度: {speed_mbps:.2f} Mbps")
        
        if speed_mbps > 1.0:
            Log.info("✓ 网络带宽充足")
            return True
        else:
            Log.warning("⚠ 网络带宽可能不足")
            return False
            
    except Exception as e:
        Log.error(f"✗ 带宽测试失败: {e}")
        return False

def main():
    """主诊断函数"""
    if len(sys.argv) != 2:
        print("用法: python3 rtmp_diagnostic.py <RTMP_URL>")
        print("示例: python3 rtmp_diagnostic.py rtmp://push.affppe.com/oakiot/YJ0QPjXfHU_cam1")
        sys.exit(1)
    
    rtmp_url = sys.argv[1]
    
    Log.info("开始RTMP推流诊断...")
    Log.info(f"目标URL: {rtmp_url}")
    Log.info("=" * 60)
    
    # 执行各项测试
    tests = [
        ("网络连接", test_network_connectivity),
        ("RTMP服务器", lambda: test_rtmp_server(rtmp_url)),
        ("网络带宽", test_bandwidth),
        ("FFmpeg推流", lambda: test_ffmpeg_rtmp(rtmp_url))
    ]
    
    results = {}
    for test_name, test_func in tests:
        Log.info("")
        try:
            results[test_name] = test_func()
        except Exception as e:
            Log.error(f"{test_name}测试异常: {e}")
            results[test_name] = False
    
    # 输出总结
    Log.info("")
    Log.info("=" * 60)
    Log.info("诊断结果总结:")
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✓ 通过" if passed else "✗ 失败"
        Log.info(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    Log.info("")
    if all_passed:
        Log.info("🎉 所有测试通过，RTMP推流应该正常工作")
    else:
        Log.error("❌ 部分测试失败，请检查网络和服务器配置")
        
        # 提供建议
        Log.info("")
        Log.info("建议:")
        if not results.get("网络连接", True):
            Log.info("  - 检查网络连接和DNS设置")
        if not results.get("RTMP服务器", True):
            Log.info("  - 检查RTMP服务器地址和端口")
            Log.info("  - 确认服务器是否正常运行")
        if not results.get("网络带宽", True):
            Log.info("  - 检查网络带宽是否足够")
            Log.info("  - 考虑降低推流码率")
        if not results.get("FFmpeg推流", True):
            Log.info("  - 检查推流密钥是否正确")
            Log.info("  - 尝试调整FFmpeg参数")
            Log.info("  - 联系服务提供商确认服务状态")

if __name__ == "__main__":
    main()
