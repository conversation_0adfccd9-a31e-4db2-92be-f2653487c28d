# 
import cv2
import spidev as SPI
#import ST7789
from lib import ST7789


disp = ST7789.ST7789()
disp.Init()
disp.clear()
disp.bl_DutyCycle(100)

# 绘制一个十字标(因为屏幕尺寸是固定的,所以使用固定坐标119,119)
def crosshair(img,w,h,point_color = (255, 255, 0)):   # 黄色十字准星,客户要求
    # 中间点坐标是119,119
    #w,h = 119,119
    #point_color = (0, 255, 0) # BGR
    thickness = 2 
    lineType = 0
    lineCable = 10
    
    if (w-lineCable)<0:   # 防止绘图坐标出现负数
        ptStart = (w,h)
    else:
        ptStart = (w - lineCable, h) #横线起始坐标
    ptEnd = (w + lineCable, h)
    cv2.line(img, ptStart, ptEnd, point_color, thickness,lineType)   # 横线
    
    if(h-lineCable)<0:
         ptStart = (w,h)
    else:
        ptStart = (w, h - lineCable)  # 线qishi
    ptEnd = (w, h + lineCable)
    cv2.line(img, ptStart, ptEnd, point_color, thickness,lineType)   # 竖线
    return img

# 顶部显示电池电量图标

#计算函数 映射
def map_power(input_power, power_min, power_max):
    # 检查范围是否合理
    if power_max <= power_min:
        raise ValueError("power_max must be greater than power_min.")
    # 计算映射值
    mapped_value = ((input_power - power_min) / (power_max - power_min)) * 100
    # 确保在0到100之间
    mapped_value = max(0, min(mapped_value, 100))
    return int(mapped_value)


# 绘制电池图标函数
def draw_battery_icon(img, power_percentage, x, y, width=20, height=10):
    # 绘制电池外框
    cv2.rectangle(img, (x, y), (x+width, y+height), (255, 255, 255), 1)
    
    # 绘制电池正极
    cv2.rectangle(img, (x+width, y+height//4), (x+width+2, y+height*3//4), (255, 255, 255), 1)
    
    # 确定电量等级 (0-25%, 26-50%, 51-75%, 76-100%)
    if power_percentage <= 25:  # 低电量，红色
        fill_color = (0, 0, 255)  # 红色 (BGR)
        fill_width = int(width * 0.25)
    elif power_percentage <= 50:  # 中低电量，黄色
        fill_color = (0, 255, 255)  # 黄色
        fill_width = int(width * 0.5)
    elif power_percentage <= 75:  # 中高电量，浅绿色
        fill_color = (0, 255, 128)  # 浅绿色
        fill_width = int(width * 0.75)
    else:  # 满电量，绿色
        fill_color = (0, 255, 0)  # 绿色
        fill_width = int(width * 1.0)
    
    # 确保填充宽度至少为1像素
    fill_width = max(1, fill_width)
    
    # 绘制填充部分
    if fill_width > 0:
        cv2.rectangle(img, (x+1, y+1), (x+fill_width, y+height-1), fill_color, -1)
    
    return img


# 底部HUD多要素数据显示 中心温度 最高温度 最低温度, 距离传感器数据
def  HUD_data(img,high,center,low,distance,power,w=240,h=240):
    
    
    fontFace = cv2.FONT_HERSHEY_SIMPLEX
    fontScale = 0.5
    fontcolor = (255, 255, 255) # BGR
    thickness = 1 
    lineType = 4
    bottomLeftOrigin = 1
    
    start = 0   # 字符
    character_height = h - 5  # 字符高度
    spacing = 51   # 字符间距
    
    # 高温区字符显示
    text_H = f'H:{high:.1f}'
    start_H = 0
    end_H = spacing +start - 1
    org = (0, h-5)
    
    cv2.rectangle(img, (start_H,h-20), (end_H,h), (191,64,64), -1)   # 绘制矩形 高温红色
    cv2.putText(img, text_H, org, fontFace, fontScale, fontcolor, thickness, lineType)
    
    # 中心温度区字符显示
    text_C = f'C:{center:.1f}'
    start_C = spacing 
    end_C = spacing*2 +start - 1
    org = (spacing, character_height)
    cv2.rectangle(img, (start_C,h-20), (end_C,h), (64,191,64), -1)   # 绘制矩形 绿色
    cv2.putText(img, text_C, org, fontFace, fontScale, (191,64,64), thickness, lineType)   # 红色字
    
    # 低温度区字符显示
    text_L = f'L:{low:.1f}'
    start_L = spacing*2
    end_L = spacing*3 - 1
    org = (start_L, character_height)
    cv2.rectangle(img, (start_L,h-20), (end_L,h), (64,64,191), -1)   # 绘制矩形 绿色
    cv2.putText(img, text_L, org, fontFace, fontScale, fontcolor, thickness, lineType)

    # 距离字符显示
    text_D = f'D:{distance:.1f}'
    start_D = spacing*3 
    end_D = spacing*4 - 1
    org = (start_D, character_height)
    cv2.rectangle(img, (start_D,h-20), (end_D,h), (191,191,64), -1)   # 绘制矩形 黄色
    cv2.putText(img, text_D, org, fontFace, fontScale, (191,64,64), thickness, lineType)   # 红色字
    
    # 电池电量
    power_max = 8.1
    power_min = 7.2
    
    # 映射电池电量 power_min ~ power_max 到 0 ~ 100%
    power_percentage = map_power(power, power_min, power_max)
    
    start_P = spacing*4 
    end_P = 239
    
    # 绘制电池背景
    cv2.rectangle(img, (start_P,h-20), (end_P,h), (64,64,64), -1)   # 深灰色背景
    
    # 绘制电池图标，位置在第五个区域的中间
    battery_x = start_P + 10
    battery_y = h - 15
    draw_battery_icon(img, power_percentage, battery_x, battery_y)


# 画面缩放裁剪 (输入图片,目标高度)
def zoom(img,target_height = 240):
    
    original_height, original_width = img.shape[:2]  # 获取原始画面的分辨率
    
    aspect_ratio = original_width / original_height   # 计算长宽比
    # 设置目标高度为240像素，计算缩放比例
    scale_factor = target_height / original_height
    new_width = int(original_width * scale_factor)
    
    # 2按照缩放比例调整尺寸
    img = cv2.resize(img, (new_width, target_height))
    # 超出宽度按两边等距截取
    if new_width > 240:
        start_x = (new_width - 240) // 2
        img = img[:, start_x:start_x + 240]
    return img