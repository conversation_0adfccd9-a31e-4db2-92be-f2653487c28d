# 热成像相机的控制

# pip install pyusb
import usb.core
import usb.util

import logging 
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s - %(lineno)s - %(message)s')
Log = logging.getLogger(__name__)



color_number = 1

dev = usb.core.find(idVendor= 0x0bda, idProduct=0x5830)  # 请替换为您的摄像头的厂商ID和产品ID

if dev is None:
    Log.error('设备未找到')
    #raise ValueError('设备未找到')


'''
if dev.is_kernel_driver_active(0):
    try:
        dev.detach_kernel_driver(0)
    except usb.core.USBError as e:
        raise ValueError("Could not detach kernel driver: %s" % str(e))

'''

# 构建VDCMD命令
bmRequestType = 0x41
bmRequest = 0x45
wValue = 0x0078
wIndex0 = 0x9d00
wIndex1 = 0x1d08


packet0 = [0x09,0xc4, 0x00, 0x00, 0x00, 0x00, 0x00,0x01]
packet1 = [0x07]

wIndex2 = 0x1d00
packet2 = [0x09,0xc4, 0x00, 0x00, 0x00, 0x00, 0x00,0x01,0x08]
#vd_cmd = [0x45,0x00,0x78, 0x9d, 0x00, 0x09,0xc4, 0x00, 0x00, 0x00, 0x00, 0x00,0x01]  # 示例命令，根据实际需求修改




# 伪彩设置
def  conf_pseudo_color(colorNum):
    bmRequestType = 0x41
    bmRequest = 0x45
    wValue = 0x0078
    wIndex = 0x1d00
    packet = [0x09,0xc4, 0x00, 0x00, 0x00, 0x00, 0x00,0x01,colorNum]
    dev.ctrl_transfer(bmRequestType, bmRequest, wValue, wIndex, packet)
    
def cfg_color():
    global color_number
    #c = [0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c]
    c = [0x02,0x05,0x0a,0x0b]
    conf_pseudo_color(c[color_number])
    color_number = color_number+1
    if color_number >= len(c):
        color_number = 0
    
if __name__ == '__main__':
    c = {0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c}
    conf_pseudo_color(0x0d)
    print('设置伪彩色成功')