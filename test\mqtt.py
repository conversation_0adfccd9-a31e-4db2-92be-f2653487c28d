# pip install paho-mqtt
import paho.mqtt.client as mqtt
import ssl
import time

# MQTT 服务器配置
broker = "l91ae194.ala.cn-hangzhou.emqxsl.cn"
port = 8883
username = "60d288936bca467f81ec70f197ad8f69"
password = "345c00b4cb9446d68210fd9b205b2dc8"
subscribe_topic = "/XF/get/60d288936bca467f81ec70f197ad8f69"
publish_topic = "/XF/post/60d288936bca467f81ec70f197ad8f69"

# MQTT 消息处理回调函数
def on_connect(client, userdata, flags, reason_code, properties):
#def on_connect(client, userdata, flags, rc):
    print("连接成功，返回码: " + str(reason_code))
    # 订阅主题
    client.subscribe(subscribe_topic)

# 消息回调
def on_message(client, userdata, msg):
    print(f"接收到消息: {msg.topic} - {msg.payload.decode()}")

# 创建 MQTT 客户端
client = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2)

# 设置用户名和密码
client.username_pw_set(username, password)
# 启用 TLS/SSL
client.tls_set(cert_reqs=ssl.CERT_REQUIRED)
# 连接到 MQTT 代理
client.on_connect = on_connect
client.on_message = on_message

try:
    client.connect(broker, port)
except Exception as e:
    print(f"连接失败: {e}")
    exit(1)

# 循环监听
client.loop_start()


while True:
    print("ok")
    time.sleep(1)   