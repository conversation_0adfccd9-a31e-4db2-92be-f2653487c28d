#!/bin/bash

echo "===== 开始安装基础环境 ====="

# 基本依赖
sudo apt update
sudo apt install -y python3-pip libusb-1.0-0-dev ffmpeg python3-pyaudio libgl1 swig python3-dev python3-setuptools systemd-resolved

# Python 包安装
pip install pyserial bleak paho-mqtt PyYAML scipy wave opencv-python pyusb pyudev --break-system-packages

# USB 工具安装
git clone https://github.com/mvp/uhubctl
cd uhubctl
make
sudo make install
cd ..

# SPI 驱动安装
wget https://github.com/joan2937/lg/archive/master.zip
unzip master.zip
cd lg-master
sudo make install
cd ..

echo "===== 基础环境安装完成 =====" 