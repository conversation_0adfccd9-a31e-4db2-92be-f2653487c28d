# 音频通讯模块
import pyaudio
import numpy as np
from scipy.signal import resample   # 重采样
import wave


    
import logging 
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s - %(message)s')
Log = logging.getLogger(__name__)    
    



class Audio:
    def __init__(self,  in_rate=44100, out_rate=8000, in_chunk=44100 // 4, out_chunk=((8000)//4),device_index=None):
        
        self.audio = pyaudio.PyAudio()
        audio_devices = self.audio.get_device_count()
        
        self.in_rate = in_rate
        self.out_rate = out_rate
        self.in_chunk = in_chunk
        self.out_chunk = out_chunk
        self.device_index  = 0
        for i in range(audio_devices):
            if device_index is None:
                info = self.audio.get_device_info_by_index(i)  # 获取所需的外置声卡
                if  'USB Audio Device'  in info['name']:
                    Log.info(info)
                    self.device_index = info['index']
        
        
    def input_init(self):   
        self.record_stream = self.audio.open(format=pyaudio.paInt16, channels=1, rate=self.in_rate, input=True,  frames_per_buffer=(self.in_rate // 4),input_device_index=self.device_index)
    def output_init(self):    
        self.play_stream = self.audio.open(format=pyaudio.paInt16, channels=1, rate= self.in_rate,  output=True, frames_per_buffer=(self.in_rate // 4),input_device_index=self.device_index)            

    def record(self):
        audio_data = self.record_stream.read(self.in_chunk, exception_on_overflow=False)
        audio_data = np.frombuffer(audio_data, dtype=np.int16)
        num_samples = int(len(audio_data) * self.out_rate / self.in_rate)
        resampled_audio_data = resample(audio_data, num_samples)
        
        audio_data = resampled_audio_data.astype(np.int16).tobytes()
        return audio_data

    def play(self, audio_data):
        audio_data = np.frombuffer(audio_data, dtype=np.int16)
        num_samples = int(len(audio_data) * self.in_rate / self.out_rate)
        
        resampled_audio_data = resample(audio_data, num_samples)
        
        audio_data = resampled_audio_data.astype(np.int16).tobytes()
        self.play_stream.write(audio_data)
    # 播放文件
    def play_wav(self, wav_file):  
        wf = wave.open(wav_file, 'rb')
        data = wf.readframes(44100//4)  # 读取数据
        while data != b'':  # 播放 
            self.play_stream.write(data)
            data = wf.readframes(44100 // 4)


if __name__ == '__main__':
    

    
    # amixer sset Speaker 37  设置喇叭音量
    # amixer sset Mic 35  # 麦克风音量
    
    
    
    import paho.mqtt.client as mqtt
    import ssl
    
    audio = Audio()  # 实例化音频对象
    #audio.play_wav('wav/ding.wav')
    
    audio.input_init()
    audio.output_init()
    
    
    # 初始化mqtt 信息
    uuid = '0000000000000000000000000000001'
    broker = 'l91ae194.ala.cn-hangzhou.emqxsl.cn'
    port = 8883
    username = '0000000000000000000000000000001'
    password = '0000000000000000000000000000001'
    
    subscribe_topic = '/XF/auido/get/60d288936bca467f81ec70f197ad8f69'
    publish_topic   = '/XF/auido/post/60d288936bca467f81ec70f197ad8f69'
    
    # MQTT 消息处理回调函数
    def on_connect(client, userdata, flags, reason_code, properties):
    #def on_connect(client, userdata, flags, rc):
        print("连接成功，返回码: " + str(reason_code))
        # 订阅主题
        client.subscribe(subscribe_topic)


    # 消息回调
    def on_message(client, userdata, msg):
        audio_data = msg.payload # 获取音频消息字节码
        audio.play(audio_data) # 写入播放流


    # 创建 MQTT 客户端
    client_mqtt = mqtt.Client(mqtt.CallbackAPIVersion.VERSION2)

    # 设置用户名和密码
    client_mqtt.username_pw_set(username, password)
    # 启用 TLS/SSL
    client_mqtt.tls_set(cert_reqs=ssl.CERT_REQUIRED)
    # 连接到 MQTT 代理
    client_mqtt.on_connect = on_connect
    client_mqtt.on_message = on_message

    # 建立服务器连接     
    try:
        client_mqtt.connect(broker, port)
    except Exception as e:
        print(f"连接失败: {e}")
        exit(1)
        
    while True:
        audio_data = audio.record()  # 录制音频
        client_mqtt.publish(publish_topic,audio_data,qos=0)  # 推送音频至mqtt
        client_mqtt.loop()
        
