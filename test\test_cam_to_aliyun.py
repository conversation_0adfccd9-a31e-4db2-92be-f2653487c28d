import subprocess

rtmp = 'rtmp://push.oakiot.com/60d288936bca467f81ec70f197ad8f69/cam1'
command = [
    'ffmpeg',
    '-loglevel', 'quiet',  # 不打印日志
    '-f', 'v4l2',
    '-input_format', 'h264',
    '-video_size', '1920x1080',
    '-i', '/dev/video2',
    '-c:v', 'copy',
    '-f', 'flv',
    rtmp
]

try:
    subprocess.run(command, check=True)
except subprocess.CalledProcessError as e:
    print(f"Command '{e.cmd}' returned non-zero exit status {e.returncode}.")