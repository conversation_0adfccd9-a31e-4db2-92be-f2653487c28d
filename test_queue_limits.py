#!/usr/bin/env python3
"""
队列限制测试脚本
用于验证队列大小限制是否有效防止内存溢出
"""

import sys
import os
import time
import threading
import psutil
import gc

# 添加当前目录到路径，以便导入q模块
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入队列模块
import q

def get_memory_usage():
    """获取当前进程的内存使用情况（MB）"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024

def test_queue_limits():
    """测试队列大小限制"""
    print("开始测试队列大小限制...")
    
    # 记录初始内存使用
    initial_memory = get_memory_usage()
    print(f"初始内存使用: {initial_memory:.2f} MB")
    
    # 测试数据
    test_data = {
        "video_frame": b"x" * 655360,  # 模拟视频帧数据 (640KB)
        "audio_data": b"x" * 44100,    # 模拟音频数据 (44KB)
        "sensor_data": {"temp": 25.5, "humidity": 60.0},  # 模拟传感器数据
        "gps_data": {"lat": 39.9042, "lng": 116.4074},    # 模拟GPS数据
        "button_event": {"button": 1, "type": "press"}     # 模拟按键事件
    }
    
    # 测试各种队列
    test_cases = [
        {"queue": q.q_video, "data": test_data["video_frame"], "name": "视频队列", "expected_max": 10},
        {"queue": q.q_lcd, "data": test_data["video_frame"], "name": "LCD队列", "expected_max": 5},
        {"queue": q.q_gps, "data": test_data["gps_data"], "name": "GPS队列", "expected_max": 20},
        {"queue": q.q_mqtt_post, "data": test_data["sensor_data"], "name": "MQTT发送队列", "expected_max": 50},
        {"queue": q.q_ble_data, "data": test_data["sensor_data"], "name": "蓝牙数据队列", "expected_max": 30},
        {"queue": q.q_play_wav, "data": test_data["audio_data"], "name": "音频播放队列", "expected_max": 10},
        {"queue": q.q_button_0, "data": test_data["button_event"], "name": "按键0队列", "expected_max": 5},
        {"queue": q.q_laser, "data": 1.25, "name": "激光距离队列", "expected_max": 20},
        {"queue": q.q_power, "data": 85, "name": "电量队列", "expected_max": 20}
    ]
    
    print("\n开始队列限制测试...")
    
    for test_case in test_cases:
        queue_obj = test_case["queue"]
        data = test_case["data"]
        name = test_case["name"]
        expected_max = test_case["expected_max"]
        
        print(f"\n测试 {name} (期望最大大小: {expected_max})")
        
        # 尝试添加超过限制的数据
        added_count = 0
        failed_count = 0
        
        for i in range(expected_max + 10):  # 尝试添加比限制多10个的数据
            try:
                queue_obj.put_nowait(data)
                added_count += 1
            except:
                failed_count += 1
        
        current_size = queue_obj.qsize()
        print(f"  - 尝试添加: {expected_max + 10} 个数据")
        print(f"  - 成功添加: {added_count} 个数据")
        print(f"  - 添加失败: {failed_count} 个数据")
        print(f"  - 当前队列大小: {current_size}")
        print(f"  - 限制是否生效: {'✓' if current_size <= expected_max else '✗'}")
        
        # 清空队列以便下次测试
        while not queue_obj.empty():
            try:
                queue_obj.get_nowait()
            except:
                break
    
    # 记录测试后的内存使用
    final_memory = get_memory_usage()
    memory_increase = final_memory - initial_memory
    
    print(f"\n内存使用情况:")
    print(f"  - 初始内存: {initial_memory:.2f} MB")
    print(f"  - 最终内存: {final_memory:.2f} MB")
    print(f"  - 内存增长: {memory_increase:.2f} MB")
    
    # 强制垃圾回收
    gc.collect()
    after_gc_memory = get_memory_usage()
    print(f"  - 垃圾回收后: {after_gc_memory:.2f} MB")
    
    return memory_increase < 50  # 如果内存增长小于50MB，认为测试通过

def test_queue_auto_cleanup():
    """测试队列自动清理功能"""
    print("\n\n开始测试队列自动清理功能...")
    
    # 导入清理函数
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    
    # 模拟清理函数（从start.py复制）
    def clear_queue_safely(queue_obj, keep_count=1, queue_name="unknown"):
        try:
            cleared_count = 0
            current_size = queue_obj.qsize()
            
            while queue_obj.qsize() > keep_count:
                try:
                    queue_obj.get_nowait()
                    cleared_count += 1
                except:
                    break
            
            if cleared_count > 0:
                print(f"队列 {queue_name} 清理了 {cleared_count} 个旧元素，当前大小: {queue_obj.qsize()}")
            
            return cleared_count
        except Exception as e:
            print(f"清理队列 {queue_name} 时发生错误: {e}")
            return 0
    
    # 测试清理功能
    test_queue = q.q_video
    test_data = b"x" * 1000  # 1KB测试数据
    
    # 填满队列
    print("填充队列到接近满载...")
    for i in range(8):  # 填充8个数据（接近10的限制）
        try:
            test_queue.put_nowait(test_data)
        except:
            break
    
    print(f"填充后队列大小: {test_queue.qsize()}")
    
    # 测试清理功能
    print("测试清理功能...")
    cleared = clear_queue_safely(test_queue, 3, "test_queue")
    print(f"清理后队列大小: {test_queue.qsize()}")
    print(f"清理功能是否正常: {'✓' if test_queue.qsize() == 3 else '✗'}")
    
    # 清空测试队列
    while not test_queue.empty():
        try:
            test_queue.get_nowait()
        except:
            break
    
    return True

def main():
    """主测试函数"""
    print("=" * 60)
    print("队列限制和内存管理测试")
    print("=" * 60)
    
    try:
        # 测试队列大小限制
        limit_test_passed = test_queue_limits()
        
        # 测试队列自动清理
        cleanup_test_passed = test_queue_auto_cleanup()
        
        print("\n" + "=" * 60)
        print("测试结果总结:")
        print(f"  - 队列大小限制测试: {'✓ 通过' if limit_test_passed else '✗ 失败'}")
        print(f"  - 队列自动清理测试: {'✓ 通过' if cleanup_test_passed else '✗ 失败'}")
        
        if limit_test_passed and cleanup_test_passed:
            print("\n🎉 所有测试通过！队列限制功能正常工作。")
            return 0
        else:
            print("\n❌ 部分测试失败，请检查队列配置。")
            return 1
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
