#!/bin/bash

echo "===== 配置自启动服务 ====="

# 创建服务文件
echo '[Unit]
Description=Python Device Service
After=network.target

[Service]
ExecStart=/usr/bin/python3 /home/<USER>/device_code/start.py
User=pi
Restart=always

[Install]
WantedBy=multi-user.target' | sudo tee /etc/systemd/system/python-work-run.service

# 启用服务
sudo systemctl enable python-work-run.service
sudo systemctl start python-work-run.service

echo "===== 服务配置完成 ====="
echo "可用以下命令控制服务："
echo "启动: sudo systemctl start python-work-run.service"
echo "停止: sudo systemctl stop python-work-run.service"
echo "状态: sudo systemctl status python-work-run.service" 